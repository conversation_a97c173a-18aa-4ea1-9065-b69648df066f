{"models": [{"model_id": "model_20250710_025728", "created_by": "admin", "created_time": "2025-07-10 02:57:28", "training_time": "2025-07-10 02:57:28.840683", "source_data": "aizhinengqingxicepingdaliu_2025-04", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6157639458091153, "cleaning_threshold": 8909.179582562627, "training_duration": 135.16145753860474, "cpu_usage": 88.6125, "memory_usage": 513.171875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/t/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_model_best.pth", "params_path": "/data/output/t/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_params.json", "scaler_path": "/data/output/t/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_scaler_y_best.pkl", "test_data_path": "/data/output/t/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_test.csv"}, "train_shape": [796, 5], "test_shape": [199, 5]}, {"model_id": "model_20250710_030858", "created_by": "admin", "created_time": "2025-07-10 03:08:58", "training_time": "2025-07-10 03:08:58.470830", "source_data": "aizhinengqingxicepingdaliu_2025-04", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6160166605903634, "cleaning_threshold": 8908.283626295313, "training_duration": 136.2143154144287, "cpu_usage": 89.76666666666667, "memory_usage": 517.671875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/t/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_model_best.pth", "params_path": "/data/output/t/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_params.json", "scaler_path": "/data/output/t/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_scaler_y_best.pkl", "test_data_path": "/data/output/t/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_test.csv"}, "train_shape": [796, 5], "test_shape": [199, 5]}, {"model_id": "model_20250710_032240", "created_by": "admin", "created_time": "2025-07-10 03:22:40", "training_time": "2025-07-10 03:22:40.451122", "source_data": "aizhinengqingxicepingdaliu_2025-04", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6138061846733368, "cleaning_threshold": 8916.049715406902, "training_duration": 139.26834845542908, "cpu_usage": 89.79166666666667, "memory_usage": 519.67578125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/t/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_model_best.pth", "params_path": "/data/output/t/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_params.json", "scaler_path": "/data/output/t/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_scaler_y_best.pkl", "test_data_path": "/data/output/t/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_test.csv"}, "train_shape": [796, 5], "test_shape": [199, 5]}, {"model_id": "model_20250710_040003", "created_by": "admin", "created_time": "2025-07-10 04:00:03", "training_time": "2025-07-10 04:00:03.667598", "source_data": "aizhinengqingxicepingdaliu_2025-04", "protocol": "TCP", "datatype": "spt_sip_dip", "r2_score": 0.7917449117411122, "cleaning_threshold": 2411.6542918831265, "training_duration": 1070.1579930782318, "cpu_usage": 95.55, "memory_usage": 583.06640625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_TCP_spt_sip_dip_model_best.pth", "params_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_TCP_spt_sip_dip_params.json", "scaler_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_TCP_spt_sip_dip_scaler_y_best.pkl", "test_data_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_TCP_spt_sip_dip_test.csv"}, "train_shape": [5760, 5], "test_shape": [1440, 5]}, {"model_id": "model_20250710_041754", "created_by": "admin", "created_time": "2025-07-10 04:17:54", "training_time": "2025-07-10 04:17:54.733834", "source_data": "aizhinengqingxicepingdaliu_2025-04", "protocol": "TCP", "datatype": "dpt_sip_dip", "r2_score": 0.7818402042979412, "cleaning_threshold": 2430.342752473217, "training_duration": 1071.0120718479156, "cpu_usage": 95.52499999999999, "memory_usage": 582.984375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_TCP_dpt_sip_dip_model_best.pth", "params_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_TCP_dpt_sip_dip_params.json", "scaler_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_TCP_dpt_sip_dip_scaler_y_best.pkl", "test_data_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_TCP_dpt_sip_dip_test.csv"}, "train_shape": [5760, 5], "test_shape": [1440, 5]}, {"model_id": "model_20250710_043539", "created_by": "admin", "created_time": "2025-07-10 04:35:39", "training_time": "2025-07-10 04:35:39.552077", "source_data": "aizhinengqingxicepingdaliu_2025-04", "protocol": "TCP", "datatype": "len_dpt_syn", "r2_score": 0.7331777138972031, "cleaning_threshold": 2307.5863638831756, "training_duration": 1064.781153678894, "cpu_usage": 95.55833333333334, "memory_usage": 582.6171875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_TCP_len_dpt_syn_model_best.pth", "params_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_TCP_len_dpt_syn_params.json", "scaler_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_TCP_len_dpt_syn_scaler_y_best.pkl", "test_data_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_TCP_len_dpt_syn_test.csv"}, "train_shape": [5760, 5], "test_shape": [1440, 5]}, {"model_id": "model_20250710_045303", "created_by": "admin", "created_time": "2025-07-10 04:53:03", "training_time": "2025-07-10 04:53:03.089086", "source_data": "aizhinengqingxicepingdaliu_2025-04", "protocol": "UDP", "datatype": "spt_sip_dip", "r2_score": 0.8501315263813671, "cleaning_threshold": 5326.387291767084, "training_duration": 1043.5016300678253, "cpu_usage": 95.66666666666667, "memory_usage": 677.16796875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_UDP_spt_sip_dip_model_best.pth", "params_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_UDP_spt_sip_dip_params.json", "scaler_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_UDP_spt_sip_dip_scaler_y_best.pkl", "test_data_path": "/data/output/t2/aizhinengqingxicepingdaliu_2025-04_UDP_spt_sip_dip_test.csv"}, "train_shape": [5760, 5], "test_shape": [1441, 5]}, {"model_id": "model_20250710_060426", "created_by": "admin", "created_time": "2025-07-10 06:04:26", "training_time": "2025-07-10 06:04:26.891001", "source_data": "aizhinengqingxicepingdaliu_2025-04", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6142919318985947, "cleaning_threshold": 8914.585969057156, "training_duration": 137.77123427391052, "cpu_usage": 89.8625, "memory_usage": 619.0078125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_model_best.pth", "params_path": "/data/output/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_params.json", "scaler_path": "/data/output/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_scaler_y_best.pkl", "test_data_path": "/data/output/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_test.csv"}, "train_shape": [796, 5], "test_shape": [199, 5]}, {"model_id": "model_20250711_070030", "created_by": "admin", "created_time": "2025-07-11 07:00:30", "training_time": "2025-07-11 07:00:30.014117", "source_data": "aizhinengqingxicepingdaliu_2025-04", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6164932435125752, "cleaning_threshold": 8906.456964725778, "training_duration": 125.41745495796204, "cpu_usage": 89.50416666666666, "memory_usage": 601.44921875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_model_best.pth", "params_path": "/data/output/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_params.json", "scaler_path": "/data/output/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_scaler_y_best.pkl", "test_data_path": "/data/output/aizhinengqingxicepingdaliu_2025-04_ICMP_dip_test.csv"}, "train_shape": [796, 5], "test_shape": [199, 5]}, {"model_id": "model_20250712_063707", "created_by": "admin", "created_time": "2025-07-12 06:37:07", "training_time": "2025-07-12 06:37:07.943743", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "spt_sip_dip", "r2_score": 0.6066912757953746, "cleaning_threshold": 10170.4114380625, "training_duration": 396.1443428993225, "cpu_usage": 96.11666666666667, "memory_usage": 602.93359375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_model_best.pth", "params_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_params.json", "scaler_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_scaler_y_best.pkl", "test_data_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_test.csv"}, "train_shape": [3791, 5], "test_shape": [948, 5]}, {"model_id": "model_20250712_064407", "created_by": "admin", "created_time": "2025-07-12 06:44:07", "training_time": "2025-07-12 06:44:07.815082", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "dpt_sip_dip", "r2_score": 0.6057749431738149, "cleaning_threshold": 10172.284923854764, "training_duration": 419.8436095714569, "cpu_usage": 96.05, "memory_usage": 627.6328125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_model_best.pth", "params_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_params.json", "scaler_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_scaler_y_best.pkl", "test_data_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_test.csv"}, "train_shape": [3791, 5], "test_shape": [948, 5]}, {"model_id": "model_20250712_065537", "created_by": "admin", "created_time": "2025-07-12 06:55:37", "training_time": "2025-07-12 06:55:37.111030", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "len_dpt_syn", "r2_score": 0.6213291170056371, "cleaning_threshold": 5615.72980739079, "training_duration": 689.2703437805176, "cpu_usage": 95.89999999999999, "memory_usage": 603.4609375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_model_best.pth", "params_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_params.json", "scaler_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_scaler_y_best.pkl", "test_data_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_test.csv"}, "train_shape": [3791, 5], "test_shape": [948, 5]}, {"model_id": "model_20250712_070651", "created_by": "admin", "created_time": "2025-07-12 07:06:51", "training_time": "2025-07-12 07:06:51.332240", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "seq_ack_dip", "r2_score": 0.596027827475909, "cleaning_threshold": 5405.047177279474, "training_duration": 674.194935798645, "cpu_usage": 96.0125, "memory_usage": 603.91796875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_model_best.pth", "params_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_params.json", "scaler_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_scaler_y_best.pkl", "test_data_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_test.csv"}, "train_shape": [3781, 5], "test_shape": [946, 5]}, {"model_id": "model_20250713_235858", "created_by": "admin", "created_time": "2025-07-13 23:58:58", "training_time": "2025-07-13 23:58:58.560453", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "UDP", "datatype": "spt_sip_dip", "r2_score": 0.7332588497099441, "cleaning_threshold": 6923.553016234046, "training_duration": 863.5747759342194, "cpu_usage": 94.45, "memory_usage": 659.828125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_UDP_spt_sip_dip_model_best.pth", "params_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_UDP_spt_sip_dip_params.json", "scaler_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_UDP_spt_sip_dip_scaler_y_best.pkl", "test_data_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_UDP_spt_sip_dip_test.csv"}, "train_shape": [3786, 5], "test_shape": [947, 5]}, {"model_id": "model_20250714_001258", "created_by": "admin", "created_time": "2025-07-14 00:12:58", "training_time": "2025-07-14 00:12:58.580194", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "UDP", "datatype": "dpt_sip_dip", "r2_score": 0.7389276695318703, "cleaning_threshold": 7892.450121070854, "training_duration": 839.9891426563263, "cpu_usage": 94.47500000000001, "memory_usage": 668.4140625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_model_best.pth", "params_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_params.json", "scaler_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_scaler_y_best.pkl", "test_data_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_test.csv"}, "train_shape": [3786, 5], "test_shape": [947, 5]}, {"model_id": "model_20250714_002707", "created_by": "admin", "created_time": "2025-07-14 00:27:07", "training_time": "2025-07-14 00:27:07.877977", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6310682717069065, "cleaning_threshold": 4188.871881155563, "training_duration": 849.2694473266602, "cpu_usage": 94.39999999999999, "memory_usage": 633.46875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_model_best.pth", "params_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_params.json", "scaler_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_scaler_y_best.pkl", "test_data_path": "/data/output/gru/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_test.csv"}, "train_shape": [3779, 5], "test_shape": [945, 5]}, {"model_id": "model_20250715_081443", "created_by": "admin", "created_time": "2025-07-15 08:14:43", "training_time": "2025-07-15 08:14:43.666194", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6076383562368253, "cleaning_threshold": 4213.533559123845, "training_duration": 1113.6713635921478, "cpu_usage": 95.69583333333333, "memory_usage": 546.07421875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_model_best.pth", "params_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_params.json", "scaler_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_scaler_y_best.pkl", "test_data_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_test.csv"}, "train_shape": [6083, 5], "test_shape": [1521, 5]}, {"model_id": "model_20250715_083302", "created_by": "admin", "created_time": "2025-07-15 08:33:02", "training_time": "2025-07-15 08:33:02.163684", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "spt_sip_dip", "r2_score": 0.6578962737131652, "cleaning_threshold": 10187.983417796118, "training_duration": 928.8119468688965, "cpu_usage": 95.59583333333335, "memory_usage": 624.39453125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_model_best.pth", "params_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_params.json", "scaler_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_scaler_y_best.pkl", "test_data_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}, {"model_id": "model_20250715_084740", "created_by": "admin", "created_time": "2025-07-15 08:47:40", "training_time": "2025-07-15 08:47:40.064483", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "dpt_sip_dip", "r2_score": 0.6579965968655573, "cleaning_threshold": 10186.00237106629, "training_duration": 877.8616240024567, "cpu_usage": 95.73333333333333, "memory_usage": 624.17578125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_model_best.pth", "params_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_params.json", "scaler_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_scaler_y_best.pkl", "test_data_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}, {"model_id": "model_20250715_090608", "created_by": "admin", "created_time": "2025-07-15 09:06:08", "training_time": "2025-07-15 09:06:08.508304", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "len_dpt_syn", "r2_score": 0.6600268248387914, "cleaning_threshold": 5468.965622162683, "training_duration": 1108.4060826301575, "cpu_usage": 95.73333333333333, "memory_usage": 624.77734375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_model_best.pth", "params_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_params.json", "scaler_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_scaler_y_best.pkl", "test_data_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}, {"model_id": "model_20250715_092114", "created_by": "admin", "created_time": "2025-07-15 09:21:14", "training_time": "2025-07-15 09:21:14.308655", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "seq_ack_dip", "r2_score": 0.6386281399244494, "cleaning_threshold": 5560.6666257286815, "training_duration": 905.7656981945038, "cpu_usage": 95.59583333333335, "memory_usage": 625.28125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_model_best.pth", "params_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_params.json", "scaler_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_scaler_y_best.pkl", "test_data_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_test.csv"}, "train_shape": [6085, 5], "test_shape": [1522, 5]}, {"model_id": "model_20250715_094018", "created_by": "admin", "created_time": "2025-07-15 09:40:18", "training_time": "2025-07-15 09:40:18.565571", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "UDP", "datatype": "spt_sip_dip", "r2_score": 0.9934140124105137, "cleaning_threshold": 6265.5722876860855, "training_duration": 1144.2195734977722, "cpu_usage": 95.54583333333333, "memory_usage": 719.9296875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_UDP_spt_sip_dip_model_best.pth", "params_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_UDP_spt_sip_dip_params.json", "scaler_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_UDP_spt_sip_dip_scaler_y_best.pkl", "test_data_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_UDP_spt_sip_dip_test.csv"}, "train_shape": [6090, 5], "test_shape": [1523, 5]}, {"model_id": "model_20250715_095913", "created_by": "admin", "created_time": "2025-07-15 09:59:13", "training_time": "2025-07-15 09:59:13.427153", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "UDP", "datatype": "dpt_sip_dip", "r2_score": 0.9933555360648082, "cleaning_threshold": 6528.984531450462, "training_duration": 1134.8161549568176, "cpu_usage": 95.6125, "memory_usage": 759.07421875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_model_best.pth", "params_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_params.json", "scaler_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_scaler_y_best.pkl", "test_data_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_test.csv"}, "train_shape": [6090, 5], "test_shape": [1523, 5]}, {"model_id": "model_20250715_101807", "created_by": "admin", "created_time": "2025-07-15 10:18:07", "training_time": "2025-07-15 10:18:07.799945", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.611627415973885, "cleaning_threshold": 4207.7286881527325, "training_duration": 1134.3364822864532, "cpu_usage": 95.63333333333333, "memory_usage": 625.00390625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_model_best.pth", "params_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_params.json", "scaler_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_scaler_y_best.pkl", "test_data_path": "/data/output/715/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_test.csv"}, "train_shape": [6083, 5], "test_shape": [1521, 5]}, {"model_id": "model_20250722_235008", "created_by": "admin", "created_time": "2025-07-22 23:50:08", "training_time": "2025-07-22 23:50:08.631402", "source_data": "C20230330-000318_2025-06", "protocol": "TCP", "datatype": "spt_sip_dip", "r2_score": -0.0007756982102036236, "cleaning_threshold": 1.4988271028548057, "training_duration": 794.4803857803345, "cpu_usage": 84.80416666666666, "memory_usage": 1717.62890625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20230330-000318_2025-06_TCP_spt_sip_dip_model_best.pth", "params_path": "/home/<USER>/C20230330-000318_2025-06_TCP_spt_sip_dip_params.json", "scaler_path": "/home/<USER>/C20230330-000318_2025-06_TCP_spt_sip_dip_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20230330-000318_2025-06_TCP_spt_sip_dip_test.csv"}, "train_shape": [4652, 5], "test_shape": [1164, 5]}, {"model_id": "model_20250722_235331", "created_by": "admin", "created_time": "2025-07-22 23:53:31", "training_time": "2025-07-22 23:53:31.844151", "source_data": "C20230330-000318_2025-06", "protocol": "TCP", "datatype": "dpt_sip_dip", "r2_score": -0.006695054012887169, "cleaning_threshold": 4.39422182745168, "training_duration": 203.1568672657013, "cpu_usage": 39.24583333333333, "memory_usage": 1872.0, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20230330-000318_2025-06_TCP_dpt_sip_dip_model_best.pth", "params_path": "/home/<USER>/C20230330-000318_2025-06_TCP_dpt_sip_dip_params.json", "scaler_path": "/home/<USER>/C20230330-000318_2025-06_TCP_dpt_sip_dip_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20230330-000318_2025-06_TCP_dpt_sip_dip_test.csv"}, "train_shape": [661, 5], "test_shape": [166, 5]}, {"model_id": "model_20250723_000009", "created_by": "admin", "created_time": "2025-07-23 00:00:09", "training_time": "2025-07-23 00:00:09.501563", "source_data": "C20230330-000318_2025-06", "protocol": "TCP", "datatype": "len_dpt_syn", "r2_score": -0.0016294140530070944, "cleaning_threshold": 2.1683670689735193, "training_duration": 397.60414576530457, "cpu_usage": 94.85416666666667, "memory_usage": 347.3828125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20230330-000318_2025-06_TCP_len_dpt_syn_model_best.pth", "params_path": "/home/<USER>/C20230330-000318_2025-06_TCP_len_dpt_syn_params.json", "scaler_path": "/home/<USER>/C20230330-000318_2025-06_TCP_len_dpt_syn_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20230330-000318_2025-06_TCP_len_dpt_syn_test.csv"}, "train_shape": [2304, 5], "test_shape": [576, 5]}, {"model_id": "model_20250723_001706", "created_by": "admin", "created_time": "2025-07-23 00:17:06", "training_time": "2025-07-23 00:17:06.449269", "source_data": "C20230330-000318_2025-06", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6323659103545831, "cleaning_threshold": 4.677233695382044, "training_duration": 862.4012546539307, "cpu_usage": 94.1375, "memory_usage": 289.30859375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_model_best.pth", "params_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_params.json", "scaler_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_test.csv"}, "train_shape": [4140, 5], "test_shape": [1035, 5]}, {"model_id": "model_20250723_070420", "created_by": "admin", "created_time": "2025-07-23 07:04:20", "training_time": "", "source_data": "", "protocol": "TCP", "datatype": "", "r2_score": -0.0007561544163190437, "cleaning_threshold": 0.0, "training_duration": 0, "cpu_usage": 0.0, "memory_usage": 0.0, "gpu_memory": 0.0, "gpu_utilization": 0.0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.001, "batch_size": 32, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20230330-000318_2025-06_TCP_spt_sip_dip_model_best.pth", "params_path": "/home/<USER>/C20230330-000318_2025-06_TCP_spt_sip_dip_params.json", "scaler_path": "/home/<USER>/C20230330-000318_2025-06_TCP_spt_sip_dip_scaler_y_best.pkl", "test_data_path": ""}, "train_shape": [4652, 5], "test_shape": [1164, 5]}, {"model_id": "model_20250723_072101", "created_by": "admin", "created_time": "2025-07-23 07:21:01", "training_time": "", "source_data": "", "protocol": "TCP", "datatype": "", "r2_score": -0.0007719711830396214, "cleaning_threshold": 0.0, "training_duration": 0, "cpu_usage": 0.0, "memory_usage": 0.0, "gpu_memory": 0.0, "gpu_utilization": 0.0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.001, "batch_size": 32, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20230330-000318_2025-06_TCP_spt_sip_dip_model_best.pth", "params_path": "/home/<USER>/C20230330-000318_2025-06_TCP_spt_sip_dip_params.json", "scaler_path": "/home/<USER>/C20230330-000318_2025-06_TCP_spt_sip_dip_scaler_y_best.pkl", "test_data_path": ""}, "train_shape": [4652, 5], "test_shape": [1164, 5]}, {"model_id": "model_20250724_000306", "created_by": "admin", "created_time": "2025-07-24 00:03:06", "training_time": "2025-07-23 23:53:15", "source_data": "C20230330-000318_2025-06", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6119011686907281, "cleaning_threshold": 4.750451415882367, "training_duration": 590.6325755119324, "cpu_usage": 0.0, "memory_usage": 545.70703125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_model_best.pth", "params_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_params.json", "scaler_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_test.csv"}, "train_shape": [4140, 5], "test_shape": [1035, 5]}, {"model_id": "model_20250724_023034", "created_by": "admin", "created_time": "2025-07-24 02:30:34", "training_time": "2025-07-24 02:20:49", "source_data": "C20230330-000318_2025-06", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.610997116025068, "cleaning_threshold": 4.755603205342898, "training_duration": 584.943886756897, "cpu_usage": 0.0, "memory_usage": 547.921875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_model_best.pth", "params_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_params.json", "scaler_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_test.csv"}, "train_shape": [4140, 5], "test_shape": [1035, 5]}, {"model_id": "model_20250724_025504", "created_by": "admin", "created_time": "2025-07-24 02:55:04", "training_time": "2025-07-24 02:45:14", "source_data": "C20230330-000318_2025-06", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6219968982625836, "cleaning_threshold": 4.690982741197355, "training_duration": 590.461817741394, "cpu_usage": 83.64041666666667, "memory_usage": 544.1953125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/output/C20230330-000318_2025-06_ICMP_dip_20250724_024515_model_best.pth", "params_path": "/data/output/C20230330-000318_2025-06_ICMP_dip_20250724_024515_params.json", "scaler_path": "/data/output/C20230330-000318_2025-06_ICMP_dip_20250724_024515_scaler_y_best.pkl", "test_data_path": "/data/output/C20230330-000318_2025-06_ICMP_dip_20250724_024515_test.csv"}, "train_shape": [4140, 5], "test_shape": [1035, 5]}, {"model_id": "model_20250724_035349", "created_by": "admin", "created_time": "2025-07-24 03:53:49", "training_time": "2025-07-24 03:44:18", "source_data": "C20230330-000318_2025-06", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6060479301491217, "cleaning_threshold": 4.784062459673679, "training_duration": 571.1020562648773, "cpu_usage": 83.77833333333335, "memory_usage": 509.81640625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_034419_model_best.pth", "params_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_034419_params.json", "scaler_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_034419_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_034419_test.csv"}, "train_shape": [4140, 5], "test_shape": [1035, 5]}, {"model_id": "model_20250724_043005", "created_by": "admin", "created_time": "2025-07-24 04:30:05", "training_time": "2025-07-24 04:20:53", "source_data": "C20230330-000318_2025-06", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.600630147279967, "cleaning_threshold": 4.815091386325443, "training_duration": 552.2055459022522, "cpu_usage": 83.99333333333334, "memory_usage": 516.57421875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_042053_model_best.pth", "params_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_042053_params.json", "scaler_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_042053_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_042053_test.csv"}, "train_shape": [4140, 5], "test_shape": [1035, 5]}, {"model_id": "model_20250724_061643", "created_by": "admin", "created_time": "2025-07-24 06:16:43", "training_time": "2025-07-24 06:06:42", "source_data": "C20230330-000318_2025-06", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6163641665340588, "cleaning_threshold": 4.724246037733986, "training_duration": 600.7270414829254, "cpu_usage": 83.7975, "memory_usage": 543.265625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_060643_model_best.pth", "params_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_060643_params.json", "scaler_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_060643_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_060643_test.csv"}, "train_shape": [4140, 5], "test_shape": [1035, 5]}, {"model_id": "model_20250724_232931", "created_by": "admin", "created_time": "2025-07-24 23:29:31", "training_time": "2025-07-24 23:23:01", "source_data": "C20230330-000318_2025-06", "protocol": "TCP", "datatype": "spt_sip_dip", "r2_score": -0.000768554930916876, "cleaning_threshold": 1.4856666822368296, "training_duration": 390.3411645889282, "cpu_usage": 7.046428571428572, "memory_usage": 742.80859375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20230330-000318_2025-06_TCP_spt_sip_dip_20250724_232302_model_best.pth", "params_path": "/home/<USER>/C20230330-000318_2025-06_TCP_spt_sip_dip_20250724_232302_params.json", "scaler_path": "/home/<USER>/C20230330-000318_2025-06_TCP_spt_sip_dip_20250724_232302_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20230330-000318_2025-06_TCP_spt_sip_dip_20250724_232302_test.csv"}, "train_shape": [4652, 5], "test_shape": [1164, 5]}, {"model_id": "model_20250725_001837", "created_by": "admin", "created_time": "2025-07-25 00:18:37", "training_time": "2025-07-25 00:00:13", "source_data": "C20240528-000695_2025-06", "protocol": "TCP", "datatype": "spt_sip_dip", "r2_score": 0.6143794195744732, "cleaning_threshold": 10.833162489706032, "training_duration": 1103.411220550537, "cpu_usage": 85.7475, "memory_usage": 6482.6484375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240528-000695_2025-06_TCP_spt_sip_dip_20250725_000054_model_best.pth", "params_path": "/home/<USER>/C20240528-000695_2025-06_TCP_spt_sip_dip_20250725_000054_params.json", "scaler_path": "/home/<USER>/C20240528-000695_2025-06_TCP_spt_sip_dip_20250725_000054_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240528-000695_2025-06_TCP_spt_sip_dip_20250725_000054_test.csv"}, "train_shape": [5016, 5], "test_shape": [1255, 5]}, {"model_id": "model_20250725_012247", "created_by": "admin", "created_time": "2025-07-25 01:22:47", "training_time": "2025-07-25 00:19:26", "source_data": "C20240528-000695_2025-06", "protocol": "TCP", "datatype": "len_dpt_syn", "r2_score": 0.8053659703175648, "cleaning_threshold": 25.560132126952578, "training_duration": 3801.8137743473053, "cpu_usage": 84.69166666666666, "memory_usage": 6335.515625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240528-000695_2025-06_TCP_len_dpt_syn_20250725_001933_model_best.pth", "params_path": "/home/<USER>/C20240528-000695_2025-06_TCP_len_dpt_syn_20250725_001933_params.json", "scaler_path": "/home/<USER>/C20240528-000695_2025-06_TCP_len_dpt_syn_20250725_001933_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240528-000695_2025-06_TCP_len_dpt_syn_20250725_001933_test.csv"}, "train_shape": [17008, 5], "test_shape": [4253, 5]}, {"model_id": "model_20250725_022733", "created_by": "admin", "created_time": "2025-07-25 02:27:33", "training_time": "2025-07-25 01:22:49", "source_data": "C20240528-000695_2025-06", "protocol": "TCP", "datatype": "seq_ack_dip", "r2_score": 0.9975520872405628, "cleaning_threshold": 11349.221086137673, "training_duration": 3884.4484803676605, "cpu_usage": 84.76625, "memory_usage": 6711.1640625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240528-000695_2025-06_TCP_seq_ack_dip_20250725_012324_model_best.pth", "params_path": "/home/<USER>/C20240528-000695_2025-06_TCP_seq_ack_dip_20250725_012324_params.json", "scaler_path": "/home/<USER>/C20240528-000695_2025-06_TCP_seq_ack_dip_20250725_012324_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240528-000695_2025-06_TCP_seq_ack_dip_20250725_012324_test.csv"}, "train_shape": [17280, 5], "test_shape": [4321, 5]}, {"model_id": "model_20250725_025948", "created_by": "admin", "created_time": "2025-07-25 02:59:48", "training_time": "2025-07-25 02:27:35", "source_data": "C20240528-000695_2025-06", "protocol": "UDP", "datatype": "spt_sip_dip", "r2_score": 0.887979345690872, "cleaning_threshold": 1419.0036276123778, "training_duration": 1933.039359331131, "cpu_usage": 85.35416666666669, "memory_usage": 4269.203125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240528-000695_2025-06_UDP_spt_sip_dip_20250725_022737_model_best.pth", "params_path": "/home/<USER>/C20240528-000695_2025-06_UDP_spt_sip_dip_20250725_022737_params.json", "scaler_path": "/home/<USER>/C20240528-000695_2025-06_UDP_spt_sip_dip_20250725_022737_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240528-000695_2025-06_UDP_spt_sip_dip_20250725_022737_test.csv"}, "train_shape": [8832, 5], "test_shape": [2209, 5]}, {"model_id": "model_20250725_033210", "created_by": "admin", "created_time": "2025-07-25 03:32:10", "training_time": "2025-07-25 02:59:48", "source_data": "C20240528-000695_2025-06", "protocol": "UDP", "datatype": "dpt_sip_dip", "r2_score": 0.8899803139112923, "cleaning_threshold": 1412.8749887749232, "training_duration": 1941.9830980300903, "cpu_usage": 85.4208333333333, "memory_usage": 4266.0546875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240528-000695_2025-06_UDP_dpt_sip_dip_20250725_025950_model_best.pth", "params_path": "/home/<USER>/C20240528-000695_2025-06_UDP_dpt_sip_dip_20250725_025950_params.json", "scaler_path": "/home/<USER>/C20240528-000695_2025-06_UDP_dpt_sip_dip_20250725_025950_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240528-000695_2025-06_UDP_dpt_sip_dip_20250725_025950_test.csv"}, "train_shape": [8832, 5], "test_shape": [2209, 5]}, {"model_id": "model_20250725_043721", "created_by": "admin", "created_time": "2025-07-25 04:37:21", "training_time": "2025-07-25 03:32:10", "source_data": "C20240528-000695_2025-06", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.7359996303711884, "cleaning_threshold": 109.33838528649595, "training_duration": 3910.4559955596924, "cpu_usage": 84.65291666666667, "memory_usage": 4281.0234375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240528-000695_2025-06_ICMP_dip_20250725_033213_model_best.pth", "params_path": "/home/<USER>/C20240528-000695_2025-06_ICMP_dip_20250725_033213_params.json", "scaler_path": "/home/<USER>/C20240528-000695_2025-06_ICMP_dip_20250725_033213_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240528-000695_2025-06_ICMP_dip_20250725_033213_test.csv"}, "train_shape": [17277, 5], "test_shape": [4320, 5]}, {"model_id": "model_20250725_051218", "created_by": "admin", "created_time": "2025-07-25 05:12:18", "training_time": "2025-07-25 05:02:11", "source_data": "C20230330-000318_2025-06", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6118759464226338, "cleaning_threshold": 4.750481238313542, "training_duration": 607.5031776428223, "cpu_usage": 83.36, "memory_usage": 571.37109375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250725_050212_model_best.pth", "params_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250725_050212_params.json", "scaler_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250725_050212_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250725_050212_test.csv"}, "train_shape": [4140, 5], "test_shape": [1035, 5]}, {"model_id": "model_20250725_054031", "created_by": "admin", "created_time": "2025-07-25 05:40:31", "training_time": "2025-07-25 05:30:41", "source_data": "C20230330-000318_2025-06", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.5993861645339369, "cleaning_threshold": 4.822272065219453, "training_duration": 590.2633907794952, "cpu_usage": 83.30000000000001, "memory_usage": 559.55078125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250725_053042_model_best.pth", "params_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250725_053042_params.json", "scaler_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250725_053042_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250725_053042_test.csv"}, "train_shape": [4140, 5], "test_shape": [1035, 5]}, {"model_id": "model_20250725_062240", "created_by": "admin", "created_time": "2025-07-25 06:22:40", "training_time": "2025-07-25 06:12:30", "source_data": "C20230330-000318_2025-06", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6170416336869665, "cleaning_threshold": 4.7203943593022, "training_duration": 609.6774001121521, "cpu_usage": 83.25374999999998, "memory_usage": 555.3828125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250725_061231_model_best.pth", "params_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250725_061231_params.json", "scaler_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250725_061231_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250725_061231_test.csv"}, "train_shape": [4140, 5], "test_shape": [1035, 5]}, {"model_id": "model_20250728_073027", "created_by": "admin", "created_time": "2025-07-28 07:30:27", "training_time": "2025-07-28 07:17:57", "source_data": "C20240702-000792_2025-06", "protocol": "TCP", "datatype": "spt_sip_dip", "r2_score": 0.8618002112305033, "cleaning_threshold": 12.546868415218105, "training_duration": 748.7248058319092, "cpu_usage": 85.25208333333332, "memory_usage": 4682.99609375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_TCP_spt_sip_dip_20250728_071800_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_TCP_spt_sip_dip_20250728_071800_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_TCP_spt_sip_dip_20250728_071800_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_TCP_spt_sip_dip_20250728_071800_test.csv"}, "train_shape": [3392, 5], "test_shape": [848, 5]}, {"model_id": "model_20250728_074200", "created_by": "admin", "created_time": "2025-07-28 07:42:00", "training_time": "2025-07-28 07:30:27", "source_data": "C20240702-000792_2025-06", "protocol": "TCP", "datatype": "dpt_sip_dip", "r2_score": 0.9773481941460334, "cleaning_threshold": 123.76250224914014, "training_duration": 693.2170221805573, "cpu_usage": 85.55250000000001, "memory_usage": 4682.36328125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_TCP_dpt_sip_dip_20250728_073029_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_TCP_dpt_sip_dip_20250728_073029_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_TCP_dpt_sip_dip_20250728_073029_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_TCP_dpt_sip_dip_20250728_073029_test.csv"}, "train_shape": [3397, 5], "test_shape": [850, 5]}, {"model_id": "model_20250728_075558", "created_by": "admin", "created_time": "2025-07-28 07:55:58", "training_time": "2025-07-28 07:42:00", "source_data": "C20240702-000792_2025-06", "protocol": "TCP", "datatype": "len_dpt_syn", "r2_score": 0.775875806420575, "cleaning_threshold": 6.286366892504417, "training_duration": 837.7982861995697, "cpu_usage": 82.45541666666666, "memory_usage": 4687.91015625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_TCP_len_dpt_syn_20250728_074202_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_TCP_len_dpt_syn_20250728_074202_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_TCP_len_dpt_syn_20250728_074202_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_TCP_len_dpt_syn_20250728_074202_test.csv"}, "train_shape": [3385, 5], "test_shape": [847, 5]}, {"model_id": "model_20250728_080825", "created_by": "admin", "created_time": "2025-07-28 08:08:25", "training_time": "2025-07-28 07:55:58", "source_data": "C20240702-000792_2025-06", "protocol": "TCP", "datatype": "seq_ack_dip", "r2_score": 0.9893371454217631, "cleaning_threshold": 582.3270917357064, "training_duration": 747.0171818733215, "cpu_usage": 85.24000000000001, "memory_usage": 4688.0390625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_TCP_seq_ack_dip_20250728_075600_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_TCP_seq_ack_dip_20250728_075600_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_TCP_seq_ack_dip_20250728_075600_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_TCP_seq_ack_dip_20250728_075600_test.csv"}, "train_shape": [3400, 5], "test_shape": [850, 5]}, {"model_id": "model_20250728_081314", "created_by": "admin", "created_time": "2025-07-28 08:13:14", "training_time": "2025-07-28 08:08:25", "source_data": "C20240702-000792_2025-06", "protocol": "UDP", "datatype": "spt_sip_dip", "r2_score": 0.9726197237157869, "cleaning_threshold": 407.8232350147129, "training_duration": 289.4688823223114, "cpu_usage": 83.02962962962964, "memory_usage": 6983.9375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_UDP_spt_sip_dip_20250728_080855_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_UDP_spt_sip_dip_20250728_080855_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_UDP_spt_sip_dip_20250728_080855_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_UDP_spt_sip_dip_20250728_080855_test.csv"}, "train_shape": [1300, 5], "test_shape": [326, 5]}, {"model_id": "model_20250728_081733", "created_by": "admin", "created_time": "2025-07-28 08:17:33", "training_time": "2025-07-28 08:13:15", "source_data": "C20240702-000792_2025-06", "protocol": "UDP", "datatype": "dpt_sip_dip", "r2_score": 0.9728754881127432, "cleaning_threshold": 407.21393125003715, "training_duration": 258.42194533348083, "cpu_usage": 82.04479166666667, "memory_usage": 6985.67578125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_UDP_dpt_sip_dip_20250728_081348_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_UDP_dpt_sip_dip_20250728_081348_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_UDP_dpt_sip_dip_20250728_081348_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_UDP_dpt_sip_dip_20250728_081348_test.csv"}, "train_shape": [1300, 5], "test_shape": [326, 5]}, {"model_id": "model_20250728_083142", "created_by": "admin", "created_time": "2025-07-28 08:31:42", "training_time": "2025-07-28 08:17:34", "source_data": "C20240702-000792_2025-06", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.9695832095820853, "cleaning_threshold": 45.15956543438691, "training_duration": 847.7989273071289, "cpu_usage": 82.55208333333333, "memory_usage": 4691.3984375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_ICMP_dip_20250728_081737_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_ICMP_dip_20250728_081737_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_ICMP_dip_20250728_081737_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_ICMP_dip_20250728_081737_test.csv"}, "train_shape": [3389, 5], "test_shape": [848, 5]}, {"model_id": "model_20250729_035637", "created_by": "admin", "created_time": "2025-07-29 03:56:37", "training_time": "2025-07-29 03:44:20", "source_data": "C20240702-000792_2025-06", "protocol": "TCP", "datatype": "spt_sip_dip", "r2_score": 0.9387245193278714, "cleaning_threshold": 25.40748009721582, "training_duration": 736.6346652507782, "cpu_usage": 85.28833333333334, "memory_usage": 4773.47265625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_TCP_spt_sip_dip_20250729_034425_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_TCP_spt_sip_dip_20250729_034425_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_TCP_spt_sip_dip_20250729_034425_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_TCP_spt_sip_dip_20250729_034425_test.csv"}, "train_shape": [3392, 5], "test_shape": [848, 5]}, {"model_id": "model_20250729_040941", "created_by": "admin", "created_time": "2025-07-29 04:09:41", "training_time": "2025-07-29 03:56:37", "source_data": "C20240702-000792_2025-06", "protocol": "TCP", "datatype": "dpt_sip_dip", "r2_score": 0.967741894211954, "cleaning_threshold": 144.18117507370363, "training_duration": 784.0499060153961, "cpu_usage": 84.59625, "memory_usage": 4773.73828125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_TCP_dpt_sip_dip_20250729_035641_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_TCP_dpt_sip_dip_20250729_035641_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_TCP_dpt_sip_dip_20250729_035641_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_TCP_dpt_sip_dip_20250729_035641_test.csv"}, "train_shape": [3397, 5], "test_shape": [850, 5]}, {"model_id": "model_20250729_042313", "created_by": "admin", "created_time": "2025-07-29 04:23:13", "training_time": "2025-07-29 04:09:41", "source_data": "C20240702-000792_2025-06", "protocol": "TCP", "datatype": "len_dpt_syn", "r2_score": 0.8855398416701376, "cleaning_threshold": 18.878160481607203, "training_duration": 812.4107546806335, "cpu_usage": 82.47, "memory_usage": 4779.3359375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_TCP_len_dpt_syn_20250729_040945_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_TCP_len_dpt_syn_20250729_040945_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_TCP_len_dpt_syn_20250729_040945_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_TCP_len_dpt_syn_20250729_040945_test.csv"}, "train_shape": [3385, 5], "test_shape": [847, 5]}, {"model_id": "model_20250729_043519", "created_by": "admin", "created_time": "2025-07-29 04:35:19", "training_time": "2025-07-29 04:23:13", "source_data": "C20240702-000792_2025-06", "protocol": "TCP", "datatype": "seq_ack_dip", "r2_score": 0.9865222211264809, "cleaning_threshold": 658.2503290179332, "training_duration": 725.7746443748474, "cpu_usage": 85.39083333333333, "memory_usage": 4779.47265625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_TCP_seq_ack_dip_20250729_042316_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_TCP_seq_ack_dip_20250729_042316_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_TCP_seq_ack_dip_20250729_042316_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_TCP_seq_ack_dip_20250729_042316_test.csv"}, "train_shape": [3400, 5], "test_shape": [850, 5]}, {"model_id": "model_20250729_043925", "created_by": "admin", "created_time": "2025-07-29 04:39:25", "training_time": "2025-07-29 04:35:19", "source_data": "C20240702-000792_2025-06", "protocol": "UDP", "datatype": "spt_sip_dip", "r2_score": 0.9651494063654952, "cleaning_threshold": 464.85783013088627, "training_duration": 246.12291073799133, "cpu_usage": 80.13630952380952, "memory_usage": 7075.66015625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_UDP_spt_sip_dip_20250729_043549_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_UDP_spt_sip_dip_20250729_043549_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_UDP_spt_sip_dip_20250729_043549_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_UDP_spt_sip_dip_20250729_043549_test.csv"}, "train_shape": [1300, 5], "test_shape": [326, 5]}, {"model_id": "model_20250729_044313", "created_by": "admin", "created_time": "2025-07-29 04:43:13", "training_time": "2025-07-29 04:39:26", "source_data": "C20240702-000792_2025-06", "protocol": "UDP", "datatype": "dpt_sip_dip", "r2_score": 0.9651494063829226, "cleaning_threshold": 464.85783009038664, "training_duration": 227.59119296073914, "cpu_usage": 80.0642857142857, "memory_usage": 7079.890625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_UDP_dpt_sip_dip_20250729_043958_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_UDP_dpt_sip_dip_20250729_043958_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_UDP_dpt_sip_dip_20250729_043958_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_UDP_dpt_sip_dip_20250729_043958_test.csv"}, "train_shape": [1300, 5], "test_shape": [326, 5]}, {"model_id": "model_20250729_045709", "created_by": "admin", "created_time": "2025-07-29 04:57:09", "training_time": "2025-07-29 04:43:14", "source_data": "C20240702-000792_2025-06", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.9619292772737434, "cleaning_threshold": 58.980296834833936, "training_duration": 834.7852625846863, "cpu_usage": 82.10208333333334, "memory_usage": 4790.90234375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_ICMP_dip_20250729_044318_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_ICMP_dip_20250729_044318_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_ICMP_dip_20250729_044318_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_ICMP_dip_20250729_044318_test.csv"}, "train_shape": [3389, 5], "test_shape": [848, 5]}, {"model_id": "model_20250729_060616", "created_by": "admin", "created_time": "2025-07-29 06:06:16", "training_time": "2025-07-29 05:05:08", "source_data": "C20240702-000792_2025-06", "protocol": "TCP", "datatype": "spt_sip_dip", "r2_score": 0.9496850237388724, "cleaning_threshold": 89.49854905986712, "training_duration": 3667.6377696990967, "cpu_usage": 84.42, "memory_usage": 4336.89453125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_TCP_spt_sip_dip_20250729_050522_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_TCP_spt_sip_dip_20250729_050522_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_TCP_spt_sip_dip_20250729_050522_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_TCP_spt_sip_dip_20250729_050522_test.csv"}, "train_shape": [16953, 5], "test_shape": [4239, 5]}, {"model_id": "model_20250729_070536", "created_by": "admin", "created_time": "2025-07-29 07:05:36", "training_time": "2025-07-29 06:06:16", "source_data": "C20240702-000792_2025-06", "protocol": "TCP", "datatype": "dpt_sip_dip", "r2_score": 0.9305145159137022, "cleaning_threshold": 217.28116993280767, "training_duration": 3559.9519679546356, "cpu_usage": 85.32333333333332, "memory_usage": 3574.90234375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_TCP_dpt_sip_dip_20250729_060624_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_TCP_dpt_sip_dip_20250729_060624_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_TCP_dpt_sip_dip_20250729_060624_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_TCP_dpt_sip_dip_20250729_060624_test.csv"}, "train_shape": [16981, 5], "test_shape": [4246, 5]}, {"model_id": "model_20250729_074307", "created_by": "admin", "created_time": "2025-07-29 07:43:07", "training_time": "2025-07-29 07:05:36", "source_data": "C20240702-000792_2025-06", "protocol": "TCP", "datatype": "len_dpt_syn", "r2_score": 0.9860614814274063, "cleaning_threshold": 89.39411407511807, "training_duration": 2250.7039082050323, "cpu_usage": 81.42619047619048, "memory_usage": 3573.4765625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_TCP_len_dpt_syn_20250729_070550_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_TCP_len_dpt_syn_20250729_070550_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_TCP_len_dpt_syn_20250729_070550_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_TCP_len_dpt_syn_20250729_070550_test.csv"}, "train_shape": [16924, 5], "test_shape": [4231, 5]}, {"model_id": "model_20250729_084419", "created_by": "admin", "created_time": "2025-07-29 08:44:19", "training_time": "2025-07-29 07:43:07", "source_data": "C20240702-000792_2025-06", "protocol": "TCP", "datatype": "seq_ack_dip", "r2_score": 0.9756598777535674, "cleaning_threshold": 717.8569114909644, "training_duration": 3672.208658695221, "cpu_usage": 84.92625, "memory_usage": 3585.31640625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_TCP_seq_ack_dip_20250729_074314_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_TCP_seq_ack_dip_20250729_074314_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_TCP_seq_ack_dip_20250729_074314_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_TCP_seq_ack_dip_20250729_074314_test.csv"}, "train_shape": [16996, 5], "test_shape": [4250, 5]}, {"model_id": "model_20250729_090806", "created_by": "admin", "created_time": "2025-07-29 09:08:06", "training_time": "2025-07-29 08:44:19", "source_data": "C20240702-000792_2025-06", "protocol": "UDP", "datatype": "spt_sip_dip", "r2_score": 0.9754664509545492, "cleaning_threshold": 668.5192681051749, "training_duration": 1426.5736861228943, "cpu_usage": 84.28083333333335, "memory_usage": 5879.83984375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_UDP_spt_sip_dip_20250729_084451_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_UDP_spt_sip_dip_20250729_084451_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_UDP_spt_sip_dip_20250729_084451_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_UDP_spt_sip_dip_20250729_084451_test.csv"}, "train_shape": [6503, 5], "test_shape": [1626, 5]}, {"model_id": "model_20250729_093201", "created_by": "admin", "created_time": "2025-07-29 09:32:01", "training_time": "2025-07-29 09:08:06", "source_data": "C20240702-000792_2025-06", "protocol": "UDP", "datatype": "dpt_sip_dip", "r2_score": 0.9752778331966607, "cleaning_threshold": 669.3388826149447, "training_duration": 1434.3436436653137, "cpu_usage": 83.98875, "memory_usage": 5880.81640625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_UDP_dpt_sip_dip_20250729_090839_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_UDP_dpt_sip_dip_20250729_090839_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_UDP_dpt_sip_dip_20250729_090839_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_UDP_dpt_sip_dip_20250729_090839_test.csv"}, "train_shape": [6503, 5], "test_shape": [1626, 5]}, {"model_id": "model_20250729_103306", "created_by": "admin", "created_time": "2025-07-29 10:33:06", "training_time": "2025-07-29 09:32:01", "source_data": "C20240702-000792_2025-06", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.9381720567570982, "cleaning_threshold": 119.15343091158242, "training_duration": 3664.619788169861, "cpu_usage": 84.41333333333334, "memory_usage": 3589.21875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 64, "num_layers": 2, "sequence_length": 30, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/home/<USER>/C20240702-000792_2025-06_ICMP_dip_20250729_093211_model_best.pth", "params_path": "/home/<USER>/C20240702-000792_2025-06_ICMP_dip_20250729_093211_params.json", "scaler_path": "/home/<USER>/C20240702-000792_2025-06_ICMP_dip_20250729_093211_scaler_y_best.pkl", "test_data_path": "/home/<USER>/C20240702-000792_2025-06_ICMP_dip_20250729_093211_test.csv"}, "train_shape": [16947, 5], "test_shape": [4237, 5]}, {"model_id": "model_20250730_041905", "created_by": "admin", "created_time": "2025-07-30 04:19:05", "training_time": "2025-07-30 04:06:46", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "spt_sip_dip", "r2_score": 0.7089514050336156, "cleaning_threshold": 2293.3455028092512, "training_duration": 739.822972536087, "cpu_usage": 84.77541666666666, "memory_usage": 642.71875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_model_best.pth", "params_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_params.json", "scaler_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_scaler_y_best.pkl", "test_data_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}, {"model_id": "model_20250730_043121", "created_by": "admin", "created_time": "2025-07-30 04:31:21", "training_time": "2025-07-30 04:19:05", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "dpt_sip_dip", "r2_score": 0.708984939499132, "cleaning_threshold": 2293.3611562814203, "training_duration": 735.1144242286682, "cpu_usage": 84.89708333333333, "memory_usage": 647.34375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_model_best.pth", "params_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_params.json", "scaler_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_scaler_y_best.pkl", "test_data_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}, {"model_id": "model_20250730_044321", "created_by": "admin", "created_time": "2025-07-30 04:43:21", "training_time": "2025-07-30 04:31:21", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "len_dpt_syn", "r2_score": 0.6394951515447225, "cleaning_threshold": 5515.330848242736, "training_duration": 720.863126039505, "cpu_usage": 85.03416666666668, "memory_usage": 644.5390625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_model_best.pth", "params_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_params.json", "scaler_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_scaler_y_best.pkl", "test_data_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}, {"model_id": "model_20250730_045602", "created_by": "admin", "created_time": "2025-07-30 04:56:02", "training_time": "2025-07-30 04:43:21", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "seq_ack_dip", "r2_score": 0.6435524210888195, "cleaning_threshold": 5583.735934553182, "training_duration": 760.143828868866, "cpu_usage": 84.95291666666667, "memory_usage": 653.37109375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_model_best.pth", "params_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_params.json", "scaler_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_scaler_y_best.pkl", "test_data_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_test.csv"}, "train_shape": [6085, 5], "test_shape": [1522, 5]}, {"model_id": "model_20250730_051532", "created_by": "admin", "created_time": "2025-07-30 05:15:32", "training_time": "2025-07-30 05:03:50", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "UDP", "datatype": "dpt_sip_dip", "r2_score": 0.6538254256467863, "cleaning_threshold": 2731.0611163928606, "training_duration": 702.2117831707001, "cpu_usage": 85.0775, "memory_usage": 728.56640625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_model_best.pth", "params_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_params.json", "scaler_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_scaler_y_best.pkl", "test_data_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_test.csv"}, "train_shape": [6089, 5], "test_shape": [1523, 5]}, {"model_id": "model_20250730_052848", "created_by": "admin", "created_time": "2025-07-30 05:28:48", "training_time": "2025-07-30 05:15:32", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6078072754455222, "cleaning_threshold": 4243.939884727835, "training_duration": 795.5266377925873, "cpu_usage": 84.83500000000001, "memory_usage": 659.546875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_model_best.pth", "params_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_params.json", "scaler_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_scaler_y_best.pkl", "test_data_path": "/data/730/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_test.csv"}, "train_shape": [6083, 5], "test_shape": [1521, 5]}, {"model_id": "aizhinengqingxicepin_model_20250801_023506", "created_by": "admin", "created_time": "2025-08-01 02:35:06", "training_time": "2025-08-01 02:35:06.891329", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "spt_sip_dip", "r2_score": 0.7091017603625768, "cleaning_threshold": 2293.200403666093, "training_duration": 837.5319554805756, "cpu_usage": 84.27583333333334, "memory_usage": 650.17578125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250801_022111_model_best.pth", "params_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250801_022111_params.json", "scaler_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250801_022111_scaler_y_best.pkl", "test_data_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250801_022111_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}, {"model_id": "aizhinengqingxicepin_model_20250801_024846", "created_by": "admin", "created_time": "2025-08-01 02:48:46", "training_time": "2025-08-01 02:48:46.194225", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "dpt_sip_dip", "r2_score": 0.7091932828317422, "cleaning_threshold": 2293.083354187117, "training_duration": 819.2588264942169, "cpu_usage": 84.58791666666666, "memory_usage": 654.82421875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250801_023507_model_best.pth", "params_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250801_023507_params.json", "scaler_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250801_023507_scaler_y_best.pkl", "test_data_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250801_023507_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}, {"model_id": "aizhinengqingxicepin_model_20250801_030221", "created_by": "admin", "created_time": "2025-08-01 03:02:21", "training_time": "2025-08-01 03:02:21.315885", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "len_dpt_syn", "r2_score": 0.6391871884061193, "cleaning_threshold": 5515.928079585729, "training_duration": 815.074215888977, "cpu_usage": 84.5325, "memory_usage": 655.171875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250801_024846_model_best.pth", "params_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250801_024846_params.json", "scaler_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250801_024846_scaler_y_best.pkl", "test_data_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250801_024846_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}, {"model_id": "aizhinengqingxicepin_model_20250801_031640", "created_by": "admin", "created_time": "2025-08-01 03:16:40", "training_time": "2025-08-01 03:16:40.170653", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "seq_ack_dip", "r2_score": 0.6439155094377652, "cleaning_threshold": 5582.959669959832, "training_duration": 858.8027698993683, "cpu_usage": 84.40541666666667, "memory_usage": 663.4921875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250801_030221_model_best.pth", "params_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250801_030221_params.json", "scaler_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250801_030221_scaler_y_best.pkl", "test_data_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250801_030221_test.csv"}, "train_shape": [6085, 5], "test_shape": [1522, 5]}, {"model_id": "aizhinengqingxicepin_model_20250801_033622", "created_by": "admin", "created_time": "2025-08-01 03:36:22", "training_time": "2025-08-01 03:36:22.140734", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "UDP", "datatype": "dpt_sip_dip", "r2_score": 0.6546214342644686, "cleaning_threshold": 2730.662458370929, "training_duration": 758.3261091709137, "cpu_usage": 84.74458333333334, "memory_usage": 690.36328125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250801_032344_model_best.pth", "params_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250801_032344_params.json", "scaler_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250801_032344_scaler_y_best.pkl", "test_data_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250801_032344_test.csv"}, "train_shape": [6089, 5], "test_shape": [1523, 5]}, {"model_id": "aizhinengqingxicepin_model_20250801_034902", "created_by": "admin", "created_time": "2025-08-01 03:49:02", "training_time": "2025-08-01 03:49:02.954371", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6085202520358753, "cleaning_threshold": 4242.830703475802, "training_duration": 760.7516424655914, "cpu_usage": 84.90416666666665, "memory_usage": 690.4921875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250801_033622_model_best.pth", "params_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250801_033622_params.json", "scaler_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250801_033622_scaler_y_best.pkl", "test_data_path": "/data/7301/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250801_033622_test.csv"}, "train_shape": [6083, 5], "test_shape": [1521, 5]}, {"model_id": "aizhinengqingxicepin_model_20250801_044035", "created_by": "admin", "created_time": "2025-08-01 04:40:35", "training_time": "2025-08-01 04:40:35.438198", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "spt_sip_dip", "r2_score": 0.7086880184062095, "cleaning_threshold": 2293.701051280698, "training_duration": 696.5399978160858, "cpu_usage": 85.07416666666667, "memory_usage": 723.375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/73011/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250801_042901_model_best.pth", "params_path": "/data/73011/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250801_042901_params.json", "scaler_path": "/data/73011/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250801_042901_scaler_y_best.pkl", "test_data_path": "/data/73011/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250801_042901_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}, {"model_id": "aizhinengqingxicepin_model_20250801_051045", "created_by": "admin", "created_time": "2025-08-01 05:10:45", "training_time": "2025-08-01 05:10:45.231472", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "spt_sip_dip", "r2_score": 0.7093663141465398, "cleaning_threshold": 2292.865009598707, "training_duration": 637.8974180221558, "cpu_usage": 85.35958333333333, "memory_usage": 803.21484375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250801_050009_model_best.pth", "params_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250801_050009_params.json", "scaler_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250801_050009_scaler_y_best.pkl", "test_data_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250801_050009_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}, {"model_id": "aizhinengqingxicepin_model_20250801_052108", "created_by": "admin", "created_time": "2025-08-01 05:21:08", "training_time": "2025-08-01 05:21:08.226185", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "dpt_sip_dip", "r2_score": 0.7093433654308096, "cleaning_threshold": 2292.9015512733945, "training_duration": 622.9537885189056, "cpu_usage": 85.49833333333333, "memory_usage": 808.40234375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250801_051045_model_best.pth", "params_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250801_051045_params.json", "scaler_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250801_051045_scaler_y_best.pkl", "test_data_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250801_051045_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}, {"model_id": "aizhinengqingxicepin_model_20250801_053120", "created_by": "admin", "created_time": "2025-08-01 05:31:20", "training_time": "2025-08-01 05:31:20.582980", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "len_dpt_syn", "r2_score": 0.6399761522907099, "cleaning_threshold": 5514.343984192199, "training_duration": 612.316737651825, "cpu_usage": 85.66791666666666, "memory_usage": 808.4453125, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250801_052108_model_best.pth", "params_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250801_052108_params.json", "scaler_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250801_052108_scaler_y_best.pkl", "test_data_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250801_052108_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}, {"model_id": "aizhinengqingxicepin_model_20250801_054151", "created_by": "admin", "created_time": "2025-08-01 05:41:51", "training_time": "2025-08-01 05:41:51.640539", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "seq_ack_dip", "r2_score": 0.6444900048447837, "cleaning_threshold": 5581.728206037895, "training_duration": 631.0196735858917, "cpu_usage": 85.51583333333333, "memory_usage": 816.27734375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250801_053120_model_best.pth", "params_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250801_053120_params.json", "scaler_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250801_053120_scaler_y_best.pkl", "test_data_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250801_053120_test.csv"}, "train_shape": [6085, 5], "test_shape": [1522, 5]}, {"model_id": "aizhinengqingxicepin_model_20250801_055738", "created_by": "admin", "created_time": "2025-08-01 05:57:38", "training_time": "2025-08-01 05:57:38.942118", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "UDP", "datatype": "dpt_sip_dip", "r2_score": 0.6507985422477911, "cleaning_threshold": 2732.544602395456, "training_duration": 644.983255147934, "cpu_usage": 85.34833333333331, "memory_usage": 885.1640625, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250801_054654_model_best.pth", "params_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250801_054654_params.json", "scaler_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250801_054654_scaler_y_best.pkl", "test_data_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250801_054654_test.csv"}, "train_shape": [6089, 5], "test_shape": [1523, 5]}, {"model_id": "aizhinengqingxicepin_model_20250801_061131", "created_by": "admin", "created_time": "2025-08-01 06:11:31", "training_time": "2025-08-01 06:11:31.603610", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "ICMP", "datatype": "dip", "r2_score": 0.6093700849085953, "cleaning_threshold": 4241.573646205026, "training_duration": 832.6177628040314, "cpu_usage": 84.43791666666665, "memory_usage": 866.58984375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250801_055739_model_best.pth", "params_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250801_055739_params.json", "scaler_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250801_055739_scaler_y_best.pkl", "test_data_path": "/data/73012/aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250801_055739_test.csv"}, "train_shape": [6083, 5], "test_shape": [1521, 5]}, {"model_id": "C20240530-000713_model_20250801_070323", "created_by": "admin", "created_time": "2025-08-01 07:03:23", "training_time": "2025-08-01 07:03:23.334808", "source_data": "C20240530-000713_2025-06", "protocol": "TCP", "datatype": "spt_sip_dip", "r2_score": 0.9333946777481276, "cleaning_threshold": 1199.5590372242457, "training_duration": 823.1102826595306, "cpu_usage": 84.0825, "memory_usage": 1272.88671875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/123456/C20240530-000713_2025-06_TCP_spt_sip_dip_20250801_064946_model_best.pth", "params_path": "/data/123456/C20240530-000713_2025-06_TCP_spt_sip_dip_20250801_064946_params.json", "scaler_path": "/data/123456/C20240530-000713_2025-06_TCP_spt_sip_dip_20250801_064946_scaler_y_best.pkl", "test_data_path": "/data/123456/C20240530-000713_2025-06_TCP_spt_sip_dip_20250801_064946_test.csv"}, "train_shape": [5636, 5], "test_shape": [1409, 5]}, {"model_id": "aizhinengqingxicepin_model_20250805_024834", "created_by": "admin", "created_time": "2025-08-05 02:48:34", "training_time": "2025-08-05 02:48:34.351104", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "spt_sip_dip", "r2_score": 0.70914104089144, "cleaning_threshold": 2293.1728024803188, "training_duration": 818.5646750926971, "cpu_usage": 84.38333333333331, "memory_usage": 729.1484375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/730000/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250805_023457_model_best.pth", "params_path": "/data/730000/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250805_023457_params.json", "scaler_path": "/data/730000/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250805_023457_scaler_y_best.pkl", "test_data_path": "/data/730000/aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250805_023457_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}, {"model_id": "aizhinengqingxicepin_model_20250805_030045", "created_by": "admin", "created_time": "2025-08-05 03:00:45", "training_time": "2025-08-05 03:00:45.263023", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "dpt_sip_dip", "r2_score": 0.7089274461419668, "cleaning_threshold": 2293.4598545414756, "training_duration": 730.8618671894073, "cpu_usage": 84.91083333333334, "memory_usage": 727.38671875, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/730000/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250805_024834_model_best.pth", "params_path": "/data/730000/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250805_024834_params.json", "scaler_path": "/data/730000/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250805_024834_scaler_y_best.pkl", "test_data_path": "/data/730000/aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250805_024834_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}, {"model_id": "aizhinengqingxicepin_model_20250805_031300", "created_by": "admin", "created_time": "2025-08-05 03:13:00", "training_time": "2025-08-05 03:13:00.678915", "source_data": "aizhinengqingxicepingdaliu_2025-07", "protocol": "TCP", "datatype": "len_dpt_syn", "r2_score": 0.641130691985734, "cleaning_threshold": 5511.950687036426, "training_duration": 735.3705866336823, "cpu_usage": 85.03208333333335, "memory_usage": 716.24609375, "gpu_memory": 0, "gpu_utilization": 0, "model_architecture": {"type": "GRU", "hidden_size": 50, "num_layers": 2, "sequence_length": 10, "dropout": 0.2}, "training_params": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100}, "file_paths": {"model_path": "/data/730000/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250805_030045_model_best.pth", "params_path": "/data/730000/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250805_030045_params.json", "scaler_path": "/data/730000/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250805_030045_scaler_y_best.pkl", "test_data_path": "/data/730000/aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250805_030045_test.csv"}, "train_shape": [6094, 5], "test_shape": [1524, 5]}]}