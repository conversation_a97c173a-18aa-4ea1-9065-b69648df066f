import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Radio,
  Upload,
  Input,
  Select,
  Button,
  Typography,
  Space,
  Divider,
  message,
  Spin,
  InputNumber,
  Slider,
  Checkbox,
  Progress,
  Alert,
  Row,
  Col,
  Statistic,
  Tabs,
} from 'antd';
import { InboxOutlined, PlayCircleOutlined, SettingOutlined, ExperimentOutlined, PlusOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { modelTrainingAPI } from '../services/api';
import useTaskManager from '../hooks/useTaskManager';
import { startMultiTrainingAsync } from '../services/taskApi';

const { Title, Text } = Typography;
const { Dragger } = Upload;
const { Option } = Select;
const { TabPane } = Tabs;

// 数据源类型定义
interface TrainingDataSource {
  id: string;
  type: 'upload' | 'local';
  file?: any;
  csvDir?: string;
  selectedFile?: string;
  outputFolder: string;
  enabled: boolean;
  availableFiles?: string[];
  filesLoading?: boolean;
}

// 训练结果展示组件
const TrainingResultDisplay: React.FC<{ resultKey: string; result: any }> = ({ resultKey, result }) => {
  const [selectedProt, selectedDatatype] = resultKey.split('_', 2);

  return (
    <div>
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        <div>
          <Text><strong>协议:</strong> {selectedProt}</Text>
          <br />
          <Text><strong>数据类型:</strong> {selectedDatatype}</Text>
        </div>

        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="训练集数据形状"
              value={result.train_shape ? `${result.train_shape[0]} × ${result.train_shape[1]}` : 'N/A'}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="测试集数据形状"
              value={result.test_shape ? `${result.test_shape[0]} × ${result.test_shape[1]}` : 'N/A'}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="R² 分数"
              value={result.r2_score ? result.r2_score.toFixed(4) : (result.r2 ? result.r2.toFixed(4) : 'N/A')}
              precision={4}
              valueStyle={{ color: result.r2_score > 0.8 || result.r2 > 0.8 ? '#3f8600' : '#cf1322' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="建议清洗阈值"
              value={result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A'}
              precision={2}
            />
          </Col>
        </Row>

        <Row gutter={16} style={{ marginTop: 16 }}>
          <Col span={6}>
            <Statistic
              title="CPU使用率"
              value={result.cpu_percent ? `${result.cpu_percent.toFixed(2)}%` : 'N/A'}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="内存使用"
              value={result.memory_mb ? `${result.memory_mb.toFixed(2)} MB` : 'N/A'}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="GPU内存"
              value={result.gpu_memory_mb ? `${result.gpu_memory_mb.toFixed(2)} MB` : (result.gpu_memory ? `${result.gpu_memory.toFixed(2)} MB` : 'N/A')}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="GPU使用率"
              value={result.gpu_utilization_percent ? `${result.gpu_utilization_percent.toFixed(2)}%` : (result.gpu_utilization ? `${result.gpu_utilization.toFixed(2)}%` : 'N/A')}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
        </Row>

        {result.train_losses && result.val_losses && (
          <div>
            <Text strong>训练损失曲线</Text>
            <div style={{ height: 300, marginTop: 8 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={result.train_losses.map((trainLoss: number, index: number) => ({
                    epoch: index + 1,
                    训练损失: trainLoss,
                    验证损失: result.val_losses[index] || null,
                  }))}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="epoch" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="训练损失"
                    stroke="#8884d8"
                    strokeWidth={2}
                    dot={false}
                  />
                  <Line
                    type="monotone"
                    dataKey="验证损失"
                    stroke="#82ca9d"
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                训练轮数: {result.train_losses.length} epochs |
                最终训练损失: {result.train_losses[result.train_losses.length - 1]?.toFixed(6)} |
                最终验证损失: {result.val_losses[result.val_losses.length - 1]?.toFixed(6)}
              </Text>
            </div>
          </div>
        )}

        {result.y_test_actual && result.y_pred && result.y_test_actual.length > 0 && (
          <div>
            <Text strong>实际值 vs 预测值对比图</Text>
            <div style={{ height: 300, marginTop: 8 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={result.y_test_actual.map((actual: number, index: number) => ({
                    index: index + 1,
                    实际值: actual,
                    预测值: result.y_pred[index],
                  }))}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="index" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="实际值"
                    stroke="#8884d8"
                    strokeWidth={2}
                    dot={false}
                  />
                  <Line
                    type="monotone"
                    dataKey="预测值"
                    stroke="#82ca9d"
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                显示所有 {result.y_test_actual.length} 个测试样本的预测对比 |
                R² 分数: {result.r2_score ? result.r2_score.toFixed(4) : (result.r2 ? result.r2.toFixed(4) : 'N/A')} |
                建议阈值: {result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A'}
              </Text>
            </div>
          </div>
        )}



        {result.model_save_path && (
          <Alert
            message="模型文件信息"
            description={
              <div>
                <p><strong>模型保存路径:</strong> {result.model_save_path}</p>
                {result.scaler_y_save_path && (
                  <p><strong>标准化器保存路径:</strong> {result.scaler_y_save_path}</p>
                )}
                {result.params_save_path && (
                  <p><strong>参数保存路径:</strong> {result.params_save_path}</p>
                )}
                {result.test_save_path && (
                  <p><strong>测试数据保存路径:</strong> {result.test_save_path}</p>
                )}
                {result.static_anomaly_threshold && (
                  <p><strong>建议清洗阈值:</strong> {result.static_anomaly_threshold.toFixed(2)}</p>
                )}
                {result.finished_time && (
                  <p><strong>训练完成时间:</strong> {result.finished_time}</p>
                )}
                {result.duration_seconds && (
                  <p><strong>训练耗时:</strong> {result.duration_seconds.toFixed(2)} 秒</p>
                )}
              </div>
            }
            type="info"
            showIcon
          />
        )}
      </Space>
    </div>
  );
};

const ModelTrainingPage: React.FC = () => {
  // 训练模式：single（单文件）或 multi（多文件）
  const [trainingMode, setTrainingMode] = useState<'single' | 'multi'>('single');

  // 单文件模式的状态（保持向后兼容）
  const [dataSource, setDataSource] = useState<'upload' | 'local'>('local');
  const [uploadedFile, setUploadedFile] = useState<any>(null);
  const [csvDir, setCsvDir] = useState('');
  const [availableFiles, setAvailableFiles] = useState<string[]>([]);
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [filesLoading, setFilesLoading] = useState(false);

  // 多文件模式的状态
  const [dataSources, setDataSources] = useState<TrainingDataSource[]>([
    {
      id: '1',
      type: 'local',
      outputFolder: '/data/output',
      enabled: true,
      availableFiles: [],
      filesLoading: false
    }
  ]);

  // 协议和数据类型选择（与Streamlit版本一致）
  const [selectedProts, setSelectedProts] = useState<string[]>(['TCP']);
  const [selectedDatatypes, setSelectedDatatypes] = useState<{[key: string]: string[]}>({
    TCP: ['spt_sip_dip']
  });

  // 训练参数
  const [learningRate, setLearningRate] = useState(0.0001);
  const [batchSize, setBatchSize] = useState(64);
  const [epochs, setEpochs] = useState(100);
  const [sequenceLength, setSequenceLength] = useState(10);
  const [hiddenSize, setHiddenSize] = useState(50);
  const [numLayers, setNumLayers] = useState(2);
  const [dropout, setDropout] = useState(0.2);
  const [outputFolder, setOutputFolder] = useState('');
  const [autoGenerateTemplate, setAutoGenerateTemplate] = useState(false); // 新增：是否自动生成清洗模板

  // 训练状态
  const [training, setTraining] = useState(false);
  const [progress, setProgress] = useState(0);
  const [trainingResults, setTrainingResults] = useState<any>(null);
  const [selectedResultKey, setSelectedResultKey] = useState<string>('');

  // 多文件训练结果状态
  const [multiTrainingResults, setMultiTrainingResults] = useState<any>(null);
  const [selectedMultiSourceKey, setSelectedMultiSourceKey] = useState<string>('');
  const [selectedMultiResultKey, setSelectedMultiResultKey] = useState<string>('');

  // 任务管理
  const { submitTrainingTask, getCompletedTasksByType, fetchCompletedTasks } = useTaskManager();
  const [useAsyncTraining, setUseAsyncTraining] = useState(true); // 默认使用异步训练

  // 多数据源管理函数
  const addDataSource = () => {
    const newSource: TrainingDataSource = {
      id: Date.now().toString(),
      type: 'local',
      outputFolder: `/data/output/model_${dataSources.length + 1}`,
      enabled: true,
      availableFiles: [],
      filesLoading: false
    };
    setDataSources([...dataSources, newSource]);
  };

  const removeDataSource = (id: string) => {
    setDataSources(dataSources.filter(source => source.id !== id));
  };

  const updateDataSource = (id: string, updates: Partial<TrainingDataSource>) => {
    setDataSources(dataSources.map(source =>
      source.id === id ? { ...source, ...updates } : source
    ));
  };

  // 异步任务结果状态
  const [asyncTrainingResults, setAsyncTrainingResults] = useState<any>(null);
  const [selectedAsyncResultKey, setSelectedAsyncResultKey] = useState<string>('');
  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState<string>('');

  // 获取已完成的训练任务
  const completedTrainingTasks = getCompletedTasksByType('training');

  // 处理异步任务选择
  const handleAsyncTaskSelect = useCallback((taskId: string) => {
    setSelectedAsyncTaskId(taskId);
    const selectedTask = completedTrainingTasks.find(task => task.task_id === taskId);
    if (selectedTask && selectedTask.result && selectedTask.result.results) {
      setAsyncTrainingResults(selectedTask.result);
      setSelectedAsyncResultKey(Object.keys(selectedTask.result.results)[0]);
    }
  }, [completedTrainingTasks]);

  // 页面加载时获取已完成任务
  useEffect(() => {
    fetchCompletedTasks();
  }, [fetchCompletedTasks]);

  // 自动选择最新的训练任务
  useEffect(() => {
    if (completedTrainingTasks.length > 0 && !selectedAsyncTaskId) {
      const latestTask = completedTrainingTasks[completedTrainingTasks.length - 1];
      handleAsyncTaskSelect(latestTask.task_id);
    }
  }, [completedTrainingTasks, selectedAsyncTaskId, handleAsyncTaskSelect]);

  // 协议和数据类型配置（与Streamlit版本完全一致）
  const protocolOptions = ['TCP', 'UDP', 'ICMP'];
  const datatypeOptions = {
    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],
    UDP: ['spt_sip_dip', 'dpt_sip_dip'],
    ICMP: ['dip']
  };

  // 获取CSV文件列表（单文件模式）
  const fetchCsvFiles = async () => {
    if (!csvDir) return;

    setFilesLoading(true);
    try {
      const response = await modelTrainingAPI.listCsvFiles(csvDir);
      setAvailableFiles(response.data.files || []);
    } catch (error: any) {
      message.error(error.response?.data?.detail || '获取文件列表失败');
      setAvailableFiles([]);
    } finally {
      setFilesLoading(false);
    }
  };

  // 获取多数据源的CSV文件列表
  const fetchCsvFilesForSource = async (sourceId: string, csvDir: string) => {
    if (!csvDir) return;

    updateDataSource(sourceId, { filesLoading: true });
    try {
      const response = await modelTrainingAPI.listCsvFiles(csvDir);
      updateDataSource(sourceId, {
        availableFiles: response.data.files || [],
        filesLoading: false
      });
    } catch (error: any) {
      message.error(error.response?.data?.detail || '获取文件列表失败');
      updateDataSource(sourceId, {
        availableFiles: [],
        filesLoading: false
      });
    }
  };

  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求
  useEffect(() => {
    if (dataSource === 'local' && csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求
      const timer = setTimeout(() => {
        fetchCsvFiles();
      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径

      return () => clearTimeout(timer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataSource, csvDir]);

  // 文件上传配置（单文件模式）
  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: '.csv',
    beforeUpload: () => false,
    onChange: (info: any) => {
      if (info.fileList.length > 0) {
        setUploadedFile(info.fileList[0]);
      } else {
        setUploadedFile(null);
      }
    },
  };

  // 多数据源文件上传配置
  const getUploadPropsForSource = (sourceId: string) => ({
    name: 'file',
    multiple: false,
    accept: '.csv',
    beforeUpload: () => false,
    onChange: (info: any) => {
      if (info.fileList.length > 0) {
        updateDataSource(sourceId, { file: info.fileList[0] });
      } else {
        updateDataSource(sourceId, { file: undefined });
      }
    },
  });

  // 协议选择变化处理（与Streamlit版本一致）
  const handleProtocolChange = (prots: string[]) => {
    setSelectedProts(prots);
    // 为新选择的协议添加默认数据类型
    const newDatatypes = { ...selectedDatatypes };
    prots.forEach(prot => {
      if (!newDatatypes[prot] && datatypeOptions[prot as keyof typeof datatypeOptions]) {
        newDatatypes[prot] = [datatypeOptions[prot as keyof typeof datatypeOptions][0]];
      }
    });
    // 移除未选择协议的数据类型
    Object.keys(newDatatypes).forEach(prot => {
      if (!prots.includes(prot)) {
        delete newDatatypes[prot];
      }
    });
    setSelectedDatatypes(newDatatypes);
  };

  // 数据类型选择变化处理
  const handleDatatypeChange = (protocol: string, datatypes: string[]) => {
    setSelectedDatatypes(prev => ({
      ...prev,
      [protocol]: datatypes
    }));
  };

  // 验证多文件训练输入
  const validateMultiTraining = (): boolean => {
    // 检查是否至少有一个有效的数据源
    const validSources = dataSources.filter(source => {
      if (source.type === 'upload') {
        return source.file && source.outputFolder;
      } else {
        return source.csvDir && source.selectedFile && source.outputFolder;
      }
    });

    if (validSources.length === 0) {
      message.error('请至少配置一个有效的数据源');
      return false;
    }

    // 检查输出路径是否重复
    const outputPaths = validSources.map(s => s.outputFolder);
    const uniquePaths = new Set(outputPaths);
    if (outputPaths.length !== uniquePaths.size) {
      message.error('模型保存路径不能重复');
      return false;
    }

    return true;
  };

  // 开始训练
  const handleStartTraining = async () => {
    // 根据训练模式进行不同的验证
    if (trainingMode === 'multi') {
      if (!validateMultiTraining()) {
        return;
      }
    } else {
      // 单文件模式验证（保持原有逻辑）
      if (dataSource === 'upload' && !uploadedFile) {
        message.error('请上传CSV文件');
        return;
      }

      if (dataSource === 'local' && (!csvDir || !selectedFile)) {
        message.error('请选择CSV文件');
        return;
      }
    }

    if (selectedProts.length === 0) {
      message.error('请至少选择一种协议');
      return;
    }

    const hasValidDatatypes = selectedProts.some(prot =>
      selectedDatatypes[prot] && selectedDatatypes[prot].length > 0
    );

    if (!hasValidDatatypes) {
      message.error('请为每个协议至少选择一种数据类型');
      return;
    }

    setTraining(true);
    setProgress(0);
    setTrainingResults(null);
    setSelectedResultKey('');

    try {
      if (useAsyncTraining) {
        // 异步训练模式
        if (trainingMode === 'multi') {
          // 多文件异步训练
          const formData = new FormData();

          // 添加文件
          dataSources.forEach((source) => {
            if (source.type === 'upload' && source.file) {
              formData.append('files', source.file.originFileObj);
            }
          });

          // 添加数据源配置
          formData.append('data_sources', JSON.stringify(dataSources));
          formData.append('selected_prots', JSON.stringify(selectedProts));
          formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));
          formData.append('learning_rate', learningRate.toString());
          formData.append('batch_size', batchSize.toString());
          formData.append('epochs', epochs.toString());
          formData.append('sequence_length', sequenceLength.toString());
          formData.append('hidden_size', hiddenSize.toString());
          formData.append('num_layers', numLayers.toString());
          formData.append('dropout', dropout.toString());
          formData.append('auto_generate_template', autoGenerateTemplate.toString());

          // 提交多文件异步任务
          const taskId = await startMultiTrainingAsync(formData);

          if (taskId) {
            message.success(`多文件训练任务已启动（共${dataSources.length}个数据源），您可以继续使用其他功能，任务完成后会收到通知`);
            // 重置状态
            setTraining(false);
            setProgress(0);
          }

          return; // 异步模式下直接返回
        } else {
          // 单文件异步训练（保持原有逻辑）
          const formData = new FormData();

          if (dataSource === 'upload') {
            formData.append('file', uploadedFile.originFileObj);
          } else {
            formData.append('csv_dir', csvDir);
            formData.append('selected_file', selectedFile);
          }

          formData.append('selected_prots', JSON.stringify(selectedProts));
          formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));
          formData.append('learning_rate', learningRate.toString());
          formData.append('batch_size', batchSize.toString());
          formData.append('epochs', epochs.toString());
          formData.append('sequence_length', sequenceLength.toString());
          formData.append('hidden_size', hiddenSize.toString());
          formData.append('num_layers', numLayers.toString());
          formData.append('dropout', dropout.toString());
          formData.append('output_folder', outputFolder);
          formData.append('auto_generate_template', autoGenerateTemplate.toString());

          // 提交异步任务
          const taskId = await submitTrainingTask(formData);

          if (taskId) {
            message.success('训练任务已启动，您可以继续使用其他功能，任务完成后会收到通知');
            // 重置状态
            setTraining(false);
            setProgress(0);
          }

          return; // 异步模式下直接返回
        }
      }

      // 同步训练模式
      let response;

      if (trainingMode === 'multi') {
        // 多文件同步训练
        const formData = new FormData();

        // 添加文件
        dataSources.forEach((source) => {
          if (source.type === 'upload' && source.file) {
            formData.append('files', source.file.originFileObj);
          }
        });

        // 添加数据源配置
        formData.append('data_sources', JSON.stringify(dataSources));
        formData.append('selected_prots', JSON.stringify(selectedProts));
        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));
        formData.append('learning_rate', learningRate.toString());
        formData.append('batch_size', batchSize.toString());
        formData.append('epochs', epochs.toString());
        formData.append('sequence_length', sequenceLength.toString());
        formData.append('hidden_size', hiddenSize.toString());
        formData.append('num_layers', numLayers.toString());
        formData.append('dropout', dropout.toString());
        formData.append('auto_generate_template', autoGenerateTemplate.toString());

        // 模拟进度更新
        const progressInterval = setInterval(() => {
          setProgress((prev) => {
            if (prev >= 90) {
              clearInterval(progressInterval);
              return prev;
            }
            return prev + 5;
          });
        }, 1000);

        response = await modelTrainingAPI.trainModelMulti(formData);
        clearInterval(progressInterval);

        // 设置多文件训练结果
        setMultiTrainingResults(response.data);
        if (response.data.results && Object.keys(response.data.results).length > 0) {
          const firstSourceKey = Object.keys(response.data.results)[0];
          setSelectedMultiSourceKey(firstSourceKey);
          if (response.data.results[firstSourceKey] && response.data.results[firstSourceKey].results) {
            setSelectedMultiResultKey(Object.keys(response.data.results[firstSourceKey].results)[0]);
          }
        }
      } else if (dataSource === 'upload') {
        const formData = new FormData();
        formData.append('file', uploadedFile.originFileObj);
        formData.append('selected_prots', JSON.stringify(selectedProts));
        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));
        formData.append('learning_rate', learningRate.toString());
        formData.append('batch_size', batchSize.toString());
        formData.append('epochs', epochs.toString());
        formData.append('sequence_length', sequenceLength.toString());
        formData.append('hidden_size', hiddenSize.toString());
        formData.append('num_layers', numLayers.toString());
        formData.append('dropout', dropout.toString());
        formData.append('output_folder', outputFolder);
        formData.append('auto_generate_template', autoGenerateTemplate.toString());

        // 模拟进度更新
        const progressInterval = setInterval(() => {
          setProgress((prev) => {
            if (prev >= 90) {
              clearInterval(progressInterval);
              return prev;
            }
            return prev + 5;
          });
        }, 1000);

        response = await modelTrainingAPI.trainModel(formData);
        clearInterval(progressInterval);
      } else {
        // 本地文件训练逻辑
        const localTrainingData = {
          csv_dir: csvDir,
          selected_file: selectedFile,
          selected_prots: selectedProts,
          selected_datatypes: selectedDatatypes,
          learning_rate: learningRate,
          batch_size: batchSize,
          epochs: epochs,
          sequence_length: sequenceLength,
          hidden_size: hiddenSize,
          num_layers: numLayers,
          dropout: dropout,
          output_folder: outputFolder,
          auto_generate_template: autoGenerateTemplate
        };

        // 模拟进度更新
        const progressInterval = setInterval(() => {
          setProgress((prev) => {
            if (prev >= 90) {
              clearInterval(progressInterval);
              return prev;
            }
            return prev + 5;
          });
        }, 1000);

        response = await modelTrainingAPI.trainModelLocal(localTrainingData);
        clearInterval(progressInterval);
      }

      setProgress(100);

      // 添加调试信息
      console.log('训练响应数据:', response.data);

      setTrainingResults(response.data);

      // 设置默认选择第一个结果
      if (response.data.results && Object.keys(response.data.results).length > 0) {
        setSelectedResultKey(Object.keys(response.data.results)[0]);
      }

      message.success('模型训练完成！');

      // 显示训练结果路径信息
      if (response.data.result_path) {
        message.info(`结果已保存至: ${response.data.result_path}`);
      }

    } catch (error: any) {
      console.error('训练错误详情:', error);
      console.error('错误响应:', error.response);

      // 更详细的错误信息
      let errorMessage = '模型训练失败';
      if (error.response) {
        if (error.response.data?.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.statusText) {
          errorMessage = `请求失败: ${error.response.status} ${error.response.statusText}`;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      message.error(errorMessage);
    } finally {
      setTraining(false);
    }
  };

  const isFormValid = () => {
    if (trainingMode === 'multi') {
      // 多文件模式验证
      const validSources = dataSources.filter(source => {
        if (source.type === 'upload') {
          return source.file && source.outputFolder;
        } else {
          return source.csvDir && source.selectedFile && source.outputFolder;
        }
      });
      return validSources.length > 0 && selectedProts.length > 0;
    } else {
      // 单文件模式验证（保持原有逻辑）
      if (dataSource === 'upload') {
        return uploadedFile && selectedProts.length > 0;
      } else {
        return csvDir && selectedFile && selectedProts.length > 0;
      }
    }
  };

  return (
    <div>
      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型训练与特征预测</Title>
      <Text type="secondary">
        上传或选择CSV文件，配置训练参数，根据多维特征训练流量检测模型，并进行特征预测。
      </Text>

      <Divider />

      {/* 训练模式选择 */}
      <Card title="训练模式" className="function-card">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Text strong>选择训练模式：</Text>
            <Radio.Group
              value={trainingMode}
              onChange={(e) => setTrainingMode(e.target.value)}
              style={{ marginTop: 8 }}
            >
              <Radio value="single">单文件训练</Radio>
              <Radio value="multi">多文件批量训练</Radio>
            </Radio.Group>
          </div>

          {trainingMode === 'multi' && (
            <Alert
              message="多文件批量训练模式"
              description="您可以添加多个数据源（本地文件或上传文件），为每个数据源配置独立的模型保存路径，系统将为每个数据源训练独立的模型。"
              type="info"
              showIcon
            />
          )}
        </Space>
      </Card>

      {/* 数据源配置 */}
      <Card title={trainingMode === 'multi' ? "数据源配置" : "数据源"} className="function-card">
        {trainingMode === 'multi' ? (
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            {/* 添加数据源按钮 */}
            <div style={{ marginBottom: 16 }}>
              <Button
                type="dashed"
                icon={<PlusOutlined />}
                onClick={addDataSource}
                style={{ width: '100%' }}
              >
                添加数据源
              </Button>
            </div>

            {/* 数据源列表 */}
            {dataSources.map((source, index) => (
              <Card
                key={source.id}
                size="small"
                title={`数据源 ${index + 1}`}
                extra={
                  dataSources.length > 1 && (
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => removeDataSource(source.id)}
                    />
                  )
                }
                style={{ marginBottom: 16 }}
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  {/* 数据源类型选择 */}
                  <div>
                    <Text strong>数据源类型：</Text>
                    <Radio.Group
                      value={source.type}
                      onChange={(e) => updateDataSource(source.id, { type: e.target.value })}
                      style={{ marginTop: 8 }}
                    >
                      <Radio value="local">本地文件</Radio>
                      <Radio value="upload">上传文件</Radio>
                    </Radio.Group>
                  </div>

                  {/* 本地文件选择 */}
                  {source.type === 'local' && (
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text strong>CSV文件目录：</Text>
                        <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>
                          <Input
                            value={source.csvDir}
                            onChange={(e) => updateDataSource(source.id, { csvDir: e.target.value })}
                            placeholder="例如: /home/<USER>"
                            style={{ flex: 1 }}
                          />
                          <Button
                            type="primary"
                            onClick={() => fetchCsvFilesForSource(source.id, source.csvDir || '')}
                            loading={source.filesLoading}
                            disabled={!source.csvDir}
                            style={{ marginLeft: 8 }}
                          >
                            刷新
                          </Button>
                        </Input.Group>
                      </div>

                      <div>
                        <Text strong>选择文件：</Text>
                        <Spin spinning={source.filesLoading || false}>
                          <Select
                            value={source.selectedFile}
                            onChange={(value) => updateDataSource(source.id, { selectedFile: value })}
                            placeholder="请选择CSV文件"
                            style={{ width: '100%', marginTop: 8 }}
                            loading={source.filesLoading}
                          >
                            {(source.availableFiles || []).map((file) => (
                              <Option key={file} value={file}>
                                {file}
                              </Option>
                            ))}
                          </Select>
                        </Spin>
                      </div>
                    </Space>
                  )}

                  {/* 文件上传 */}
                  {source.type === 'upload' && (
                    <div>
                      <Text strong>上传文件：</Text>
                      <Upload {...getUploadPropsForSource(source.id)} style={{ marginTop: 8 }}>
                        <Button icon={<UploadOutlined />}>选择CSV文件</Button>
                      </Upload>
                      {source.file && (
                        <div style={{ marginTop: 8 }}>
                          <Text type="secondary">已选择: {source.file.name}</Text>
                        </div>
                      )}
                    </div>
                  )}

                  {/* 输出路径配置 */}
                  <div>
                    <Card
                      size="small"
                      title={
                        <Space>
                          <InboxOutlined />
                          <Text strong>模型保存路径</Text>
                        </Space>
                      }
                      style={{ marginTop: 8 }}
                    >
                      <Input
                        value={source.outputFolder}
                        onChange={(e) => updateDataSource(source.id, { outputFolder: e.target.value })}
                        placeholder="例如: /data/output"
                        size="large"
                        prefix={<InboxOutlined />}
                      />
                    </Card>
                  </div>
                </Space>
              </Card>
            ))}
          </Space>
        ) : (
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div>
              <Text strong>训练数据源：</Text>
              <Radio.Group
                value={dataSource}
                onChange={(e) => setDataSource(e.target.value)}
                style={{ marginTop: 8 }}
              >
                <Radio value="local">选择本地CSV文件</Radio>
                <Radio value="upload">上传CSV文件</Radio>
              </Radio.Group>
            </div>

            {/* 本地文件选择 */}
            {dataSource === 'local' && (
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>CSV文件目录：</Text>
                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>
                  <Input
                    value={csvDir}
                    onChange={(e) => setCsvDir(e.target.value)}
                    placeholder="例如: /home/<USER>"
                    style={{ flex: 1 }}
                  />
                  <Button
                    type="primary"
                    onClick={fetchCsvFiles}
                    loading={filesLoading}
                    disabled={!csvDir}
                    style={{ marginLeft: 8 }}
                  >
                    刷新
                  </Button>
                </Input.Group>
              </div>

              <div>
                <Text strong>选择文件：</Text>
                <Spin spinning={filesLoading}>
                  <Select
                    value={selectedFile}
                    onChange={setSelectedFile}
                    placeholder="请选择CSV文件"
                    style={{ width: '100%', marginTop: 8 }}
                    loading={filesLoading}
                  >
                    {availableFiles.map((file) => (
                      <Option key={file} value={file}>
                        {file}
                      </Option>
                    ))}
                  </Select>
                </Spin>
              </div>
            </Space>
          )}

          {/* 文件上传 */}
          {dataSource === 'upload' && (
            <div>
              <Text strong>上传文件：</Text>
              <Dragger {...uploadProps} style={{ marginTop: 8 }}>
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽CSV文件到此区域上传</p>
                <p className="ant-upload-hint">
                  支持CSV格式的流量数据文件
                </p>
              </Dragger>
            </div>
          )}
          </Space>
        )}
      </Card>

      {/* 协议和数据类型选择 */}
      <Card title="协议和数据类型选择" className="function-card">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Text strong>选择协议：</Text>
            <Select
              mode="multiple"
              value={selectedProts}
              onChange={handleProtocolChange}
              placeholder="请选择协议"
              style={{ width: '100%', marginTop: 8 }}
            >
              {protocolOptions.map((prot) => (
                <Option key={prot} value={prot}>
                  {prot}
                </Option>
              ))}
            </Select>
          </div>

          {selectedProts.map((prot) => (
            <div key={prot}>
              <Text strong>{prot} 数据类型：</Text>
              <Checkbox.Group
                value={selectedDatatypes[prot] || []}
                onChange={(datatypes) => handleDatatypeChange(prot, datatypes as string[])}
                style={{ marginTop: 8 }}
              >
                {(datatypeOptions[prot as keyof typeof datatypeOptions] || []).map((datatype) => (
                  <Checkbox key={datatype} value={datatype}>
                    {datatype}
                  </Checkbox>
                ))}
              </Checkbox.Group>
            </div>
          ))}
        </Space>
      </Card>

      {/* 训练参数配置 */}
      <Card
        title={
          <Space>
            <SettingOutlined />
            <span>训练参数配置</span>
          </Space>
        }
        className="function-card"
      >
        <Row gutter={[24, 24]}>
          {/* 基础参数 */}
          <Col span={12}>
            <Card
              size="small"
              title={
                <Space>
                  <ExperimentOutlined />
                  <Text strong>基础参数</Text>
                </Space>
              }
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <Row align="middle">
                  <Col span={10}>
                    <Text strong>学习率：</Text>
                  </Col>
                  <Col span={14}>
                    <InputNumber
                      value={learningRate}
                      onChange={(value) => setLearningRate(value || 0.0001)}
                      min={0.0001}
                      max={1}
                      step={0.0001}
                      style={{ width: '100%' }}
                      placeholder="0.0001"
                    />
                  </Col>
                </Row>
                <Row align="middle">
                  <Col span={10}>
                    <Text strong>批量大小：</Text>
                  </Col>
                  <Col span={14}>
                    <InputNumber
                      value={batchSize}
                      onChange={(value) => setBatchSize(value || 64)}
                      min={1}
                      max={512}
                      style={{ width: '100%' }}
                      placeholder="64"
                    />
                  </Col>
                </Row>
                <Row align="middle">
                  <Col span={10}>
                    <Text strong>训练轮数：</Text>
                  </Col>
                  <Col span={14}>
                    <InputNumber
                      value={epochs}
                      onChange={(value) => setEpochs(value || 100)}
                      min={1}
                      max={1000}
                      style={{ width: '100%' }}
                      placeholder="100"
                    />
                  </Col>
                </Row>
              </Space>
            </Card>
          </Col>

          {/* 模型参数 */}
          <Col span={12}>
            <Card
              size="small"
              title={
                <Space>
                  <SettingOutlined />
                  <Text strong>模型参数</Text>
                </Space>
              }
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <Row align="middle">
                  <Col span={10}>
                    <Text strong>序列长度：</Text>
                  </Col>
                  <Col span={14}>
                    <InputNumber
                      value={sequenceLength}
                      onChange={(value) => setSequenceLength(value || 10)}
                      min={1}
                      max={100}
                      style={{ width: '100%' }}
                      placeholder="10"
                    />
                  </Col>
                </Row>
                <Row align="middle">
                  <Col span={10}>
                    <Text strong>隐藏层大小：</Text>
                  </Col>
                  <Col span={14}>
                    <InputNumber
                      value={hiddenSize}
                      onChange={(value) => setHiddenSize(value || 50)}
                      min={10}
                      max={512}
                      step={10}
                      style={{ width: '100%' }}
                      placeholder="50"
                    />
                  </Col>
                </Row>
                <Row align="middle">
                  <Col span={10}>
                    <Text strong>层数：</Text>
                  </Col>
                  <Col span={14}>
                    <InputNumber
                      value={numLayers}
                      onChange={(value) => setNumLayers(value || 2)}
                      min={1}
                      max={10}
                      style={{ width: '100%' }}
                      placeholder="2"
                    />
                  </Col>
                </Row>
                <div style={{ width: '100%' }}>
                  <Row align="middle" style={{ marginBottom: 8 }}>
                    <Col span={12}>
                      <Text strong>Dropout 概率：</Text>
                    </Col>
                    <Col span={12} style={{ textAlign: 'right' }}>
                      <Text code>{dropout}</Text>
                    </Col>
                  </Row>
                  <Slider
                    value={dropout}
                    onChange={setDropout}
                    min={0}
                    max={0.9}
                    step={0.05}
                    marks={{
                      0: '0',
                      0.2: '0.2',
                      0.5: '0.5',
                      0.9: '0.9'
                    }}
                  />
                </div>
              </Space>
            </Card>
          </Col>
        </Row>

        {/* 模型保存路径（仅在单文件模式下显示） */}
        {trainingMode === 'single' && (
          <Row style={{ marginTop: 24 }}>
            <Col span={24}>
              <Card
                size="small"
                title={
                  <Space>
                    <InboxOutlined />
                    <Text strong>模型保存路径</Text>
                  </Space>
                }
              >
                <Input
                  value={outputFolder}
                  onChange={(e) => setOutputFolder(e.target.value)}
                  placeholder="例如: /data/output"
                  size="large"
                  prefix={<InboxOutlined />}
                />
              </Card>
            </Col>
          </Row>
        )}
      </Card>

      {/* 清洗模板生成选项 */}
      <Card className="function-card" title="清洗模板生成">
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <div>
            <Text strong>自动生成清洗模板：</Text>
            <div style={{ marginTop: 8 }}>
              <Checkbox
                checked={autoGenerateTemplate}
                onChange={(e) => setAutoGenerateTemplate(e.target.checked)}
              >
                训练完成后自动生成清洗模板
              </Checkbox>
            </div>
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                选择此选项后，系统将在模型训练完成后自动调用清洗模板生成功能，
                根据训练结果中的阈值信息生成相应的清洗模板文件。
              </Text>
            </div>
          </div>
        </Space>
      </Card>

      {/* 训练模式选择 */}
      <Card className="function-card" title="训练模式">
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <div>
            <Text strong>选择训练模式：</Text>
            <Radio.Group
              value={useAsyncTraining}
              onChange={(e) => setUseAsyncTraining(e.target.value)}
              style={{ marginTop: 8 }}
            >
              <Radio value={true}>
                <Space>
                  异步训练（推荐）
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    - 后台运行，不阻塞其他操作
                  </Text>
                </Space>
              </Radio>
              <Radio value={false}>
                <Space>
                  同步训练
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    - 等待训练完成，期间无法使用其他功能
                  </Text>
                </Space>
              </Radio>
            </Radio.Group>
          </div>

          {useAsyncTraining && (
            <Alert
              message="异步训练模式"
              description={
                <div>
                  训练任务将在后台运行，您可以继续使用系统的其他功能。
                  <br />
                  任务完成后会收到通知，可在 <strong>侧边栏菜单 → 任务管理</strong> 中查看进度和结果。
                </div>
              }
              type="info"
              showIcon
            />
          )}
        </Space>
      </Card>

      {/* 开始训练按钮 */}
      <Card className="function-card">
        <Button
          type="primary"
          size="large"
          icon={<PlayCircleOutlined />}
          onClick={handleStartTraining}
          loading={training}
          disabled={!isFormValid()}
          className="action-button"
        >
          {training ? '正在训练...' : '开始训练预测'}
        </Button>

        {/* 训练进度 */}
        {training && (
          <div className="progress-section">
            <Text>训练进度：</Text>
            <Progress percent={progress} status="active" />
          </div>
        )}

        {/* 训练结果展示 */}
        {trainingResults && trainingResults.results && (
          <div style={{ marginTop: 24 }}>
            <Alert
              message="训练完成"
              description={
                <div>
                  <p>所有模型训练完成！</p>
                  {trainingResults.result_path && (
                    <p><strong>结果已更新至:</strong> {trainingResults.result_path}</p>
                  )}
                  {trainingResults.template_info && (
                    <div style={{ marginTop: 8, padding: 8, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 4 }}>
                      {trainingResults.template_info.template_generated ? (
                        <div>
                          <p style={{ color: '#52c41a', fontWeight: 'bold' }}>✅ 清洗模板已自动生成</p>
                          <p><strong>模板路径:</strong> {trainingResults.template_info.template_path}</p>
                          <p><strong>更新阈值数量:</strong> {trainingResults.template_info.updated_thresholds}</p>
                        </div>
                      ) : (
                        <div>
                          <p style={{ color: '#ff4d4f', fontWeight: 'bold' }}>❌ 清洗模板生成失败</p>
                          <p><strong>错误信息:</strong> {trainingResults.template_info.error}</p>
                        </div>
                      )}
                    </div>
                  )}
                  {Object.entries(trainingResults.results).map(([key, result]: [string, any]) => (
                    <div key={key} style={{ marginTop: 8 }}>
                      <p><strong>协议与数据类型:</strong> {key}</p>
                      <p>模型已保存至: {result.model_save_path}</p>
                      <p>标准化器已保存至: {result.scaler_y_save_path}</p>
                    </div>
                  ))}
                </div>
              }
              type="success"
              showIcon
              style={{ marginTop: 16 }}
            />
          </div>
        )}

      {/* 查看模型训练及特征预测结果（单文件模式） */}
      {trainingResults && trainingResults.results && Object.keys(trainingResults.results).length > 0 && (
        <Card title="查看模型训练及特征预测结果" className="function-card">
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div>
              <Text strong>选择要查看的协议和数据类型：</Text>
              <Select
                value={selectedResultKey}
                onChange={setSelectedResultKey}
                style={{ width: '100%', marginTop: 8 }}
                placeholder="请选择协议和数据类型"
              >
                {Object.keys(trainingResults.results).map((key) => (
                  <Option key={key} value={key}>
                    {key}
                  </Option>
                ))}
              </Select>
            </div>

            {selectedResultKey && trainingResults.results[selectedResultKey] && (
              <TrainingResultDisplay
                resultKey={selectedResultKey}
                result={trainingResults.results[selectedResultKey]}
              />
            )}
          </Space>
        </Card>
      )}

      {/* 多文件训练结果展示 */}
      {multiTrainingResults && multiTrainingResults.results && Object.keys(multiTrainingResults.results).length > 0 && (
        <Card title="多文件训练结果" className="function-card">
          <Alert
            message="多文件训练完成"
            description={`成功处理 ${Object.keys(multiTrainingResults.results).length} 个数据源的训练任务`}
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Tabs>
            {Object.entries(multiTrainingResults.results).map(([sourceId, sourceResult]: [string, any]) => (
              <TabPane tab={`数据源 ${sourceId}`} key={sourceId}>
                {sourceResult.error ? (
                  <Alert
                    message="训练失败"
                    description={sourceResult.error}
                    type="error"
                    showIcon
                  />
                ) : (
                  <div>
                    {/* 显示该数据源的所有协议/数据类型结果 */}
                    {sourceResult.results && Object.keys(sourceResult.results).length > 0 ? (
                      <Space direction="vertical" size="large" style={{ width: '100%' }}>
                        <div>
                          <Text strong>选择要查看的协议和数据类型：</Text>
                          <Select
                            value={selectedMultiSourceKey === sourceId ? selectedMultiResultKey : ''}
                            onChange={(value) => {
                              setSelectedMultiSourceKey(sourceId);
                              setSelectedMultiResultKey(value);
                            }}
                            style={{ width: '100%', marginTop: 8 }}
                            placeholder="请选择协议和数据类型"
                          >
                            {Object.keys(sourceResult.results).map((key) => (
                              <Option key={key} value={key}>
                                {key}
                              </Option>
                            ))}
                          </Select>
                        </div>

                        {selectedMultiSourceKey === sourceId && selectedMultiResultKey && sourceResult.results[selectedMultiResultKey] && (
                          <TrainingResultDisplay
                            resultKey={selectedMultiResultKey}
                            result={sourceResult.results[selectedMultiResultKey]}
                          />
                        )}
                      </Space>
                    ) : (
                      <Alert
                        message="无训练结果"
                        description="该数据源没有生成有效的训练结果"
                        type="warning"
                        showIcon
                      />
                    )}
                  </div>
                )}
              </TabPane>
            ))}
          </Tabs>
        </Card>
      )}

      {/* 异步训练结果展示 */}
      {completedTrainingTasks.length > 0 && (
        <Card title="异步训练结果" className="function-card" style={{ marginTop: 24 }}>
          <Alert
            message="异步训练已完成"
            description="以下是后台训练任务的结果，您可以查看不同协议和数据类型的训练效果。"
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            {/* 任务选择器 */}
            <div>
              <Text strong>选择训练任务：</Text>
              <Select
                value={selectedAsyncTaskId}
                onChange={handleAsyncTaskSelect}
                style={{ width: '100%', marginTop: 8 }}
                placeholder="请选择要查看的训练任务"
              >
                {completedTrainingTasks.map((task) => (
                  <Option key={task.task_id} value={task.task_id}>
                    {task.task_id.includes('_') ?
                      `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` :
                      `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`
                    }
                  </Option>
                ))}
              </Select>
            </div>

            {/* 模板生成信息显示 */}
            {asyncTrainingResults && asyncTrainingResults.template_info && (
              <div style={{ marginTop: 8, padding: 8, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 4 }}>
                {asyncTrainingResults.template_info.template_generated ? (
                  <div>
                    <p style={{ color: '#52c41a', fontWeight: 'bold' }}>✅ 清洗模板已自动生成</p>
                    <p><strong>模板路径:</strong> {asyncTrainingResults.template_info.template_path}</p>
                    <p><strong>更新阈值数量:</strong> {asyncTrainingResults.template_info.updated_thresholds}</p>
                  </div>
                ) : (
                  <div>
                    <p style={{ color: '#ff4d4f', fontWeight: 'bold' }}>❌ 清洗模板生成失败</p>
                    <p><strong>错误信息:</strong> {asyncTrainingResults.template_info.error}</p>
                  </div>
                )}
              </div>
            )}

            {/* 协议和数据类型选择器 */}
            {asyncTrainingResults && asyncTrainingResults.results && (
              <div>
                <Text strong>选择要查看的协议和数据类型：</Text>
                <Select
                  value={selectedAsyncResultKey}
                  onChange={setSelectedAsyncResultKey}
                  style={{ width: '100%', marginTop: 8 }}
                  placeholder="请选择协议和数据类型"
                >
                  {Object.keys(asyncTrainingResults.results).map((key) => (
                    <Option key={key} value={key}>
                      {key}
                    </Option>
                  ))}
                </Select>
              </div>
            )}

            {/* 结果展示 */}
            {selectedAsyncResultKey && asyncTrainingResults && asyncTrainingResults.results[selectedAsyncResultKey] && (
              <TrainingResultDisplay
                resultKey={selectedAsyncResultKey}
                result={asyncTrainingResults.results[selectedAsyncResultKey]}
              />
            )}
          </Space>
        </Card>
      )}
      </Card>
    </div>
  );
};

export default ModelTrainingPage;
