[{"/home/<USER>/frontend-react-stable/src/index.tsx": "19", "/home/<USER>/frontend-react-stable/src/store/store.ts": "20", "/home/<USER>/frontend-react-stable/src/App.tsx": "21", "/home/<USER>/frontend-react-stable/src/store/slices/authSlice.ts": "23", "/home/<USER>/frontend-react-stable/src/store/slices/uiSlice.ts": "22", "/home/<USER>/frontend-react-stable/src/pages/LoginPage.tsx": "24", "/home/<USER>/frontend-react-stable/src/pages/CleanTemplatePage.tsx": "31", "/home/<USER>/frontend-react-stable/src/pages/ModelPredictionPage.tsx": "28", "/home/<USER>/frontend-react-stable/src/pages/ModelRegistryPage.tsx": "26", "/home/<USER>/frontend-react-stable/src/pages/UserManagementPage.tsx": "29", "/home/<USER>/frontend-react-stable/src/pages/DataCleaningPage.tsx": "25", "/home/<USER>/frontend-react-stable/src/pages/ModelTrainingPage.tsx": "27", "/home/<USER>/frontend-react-stable/src/pages/DataQueryPage.tsx": "30", "/home/<USER>/frontend-react-stable/src/components/Layout/MainLayout.tsx": "32", "/home/<USER>/frontend-react-stable/src/services/api.ts": "33", "/home/<USER>/frontend-react-stable/src/hooks/useTaskManager.ts": "34", "/home/<USER>/frontend-react-stable/src/services/taskApi.ts": "35", "/home/<USER>/frontend-react-stable/src/components/TaskStatusIndicator.tsx": "36", "/home/<USER>/frontend-react-stable/src/pages/TaskManagerPage.tsx": "37"}, {"size": 645, "mtime": 1752127228213, "results": "38", "hashOfConfig": "39"}, {"size": 507, "mtime": 1752120635095, "results": "40", "hashOfConfig": "39"}, {"size": 2437, "mtime": 1752120724910, "results": "41", "hashOfConfig": "39"}, {"size": 4469, "mtime": 1752206161486, "results": "42", "hashOfConfig": "39"}, {"size": 1922, "mtime": 1752120668735, "results": "43", "hashOfConfig": "39"}, {"size": 3989, "mtime": 1752120790402, "results": "44", "hashOfConfig": "39"}, {"size": 31450, "mtime": 1752476997349, "results": "45", "hashOfConfig": "39"}, {"size": 35798, "mtime": 1752459504380, "results": "46", "hashOfConfig": "39"}, {"size": 20184, "mtime": 1752463975609, "results": "47", "hashOfConfig": "39"}, {"size": 13535, "mtime": 1752461558939, "results": "48", "hashOfConfig": "39"}, {"size": 9375, "mtime": 1752222046474, "results": "49", "hashOfConfig": "39"}, {"size": 33614, "mtime": 1752459469227, "results": "50", "hashOfConfig": "39"}, {"size": 17586, "mtime": 1752461427498, "results": "51", "hashOfConfig": "39"}, {"size": 6627, "mtime": 1752477338379, "results": "52", "hashOfConfig": "39"}, {"size": 7722, "mtime": 1752472687466, "results": "53", "hashOfConfig": "39"}, {"size": 8532, "mtime": 1752231957408, "results": "54", "hashOfConfig": "39"}, {"size": 6522, "mtime": 1752227779565, "results": "55", "hashOfConfig": "39"}, {"size": 4770, "mtime": 1752231993723, "results": "56", "hashOfConfig": "39"}, {"size": 645, "mtime": 1752570960367, "results": "57", "hashOfConfig": "58"}, {"size": 507, "mtime": 1752570960367, "results": "59", "hashOfConfig": "58"}, {"size": 2801, "mtime": 1753877515115, "results": "60", "hashOfConfig": "58"}, {"size": 1922, "mtime": 1752570960368, "results": "61", "hashOfConfig": "58"}, {"size": 4951, "mtime": 1753877486888, "results": "62", "hashOfConfig": "58"}, {"size": 3894, "mtime": 1754373213964, "results": "63", "hashOfConfig": "58"}, {"size": 20033, "mtime": 1754366077864, "results": "64", "hashOfConfig": "58"}, {"size": 20184, "mtime": 1752570960369, "results": "65", "hashOfConfig": "58"}, {"size": 58762, "mtime": 1754381531618, "results": "66", "hashOfConfig": "58"}, {"size": 44681, "mtime": 1754381300213, "results": "67", "hashOfConfig": "58"}, {"size": 13069, "mtime": 1754362693317, "results": "68", "hashOfConfig": "58"}, {"size": 16628, "mtime": 1754362997476, "results": "69", "hashOfConfig": "58"}, {"size": 31450, "mtime": 1752570960369, "results": "70", "hashOfConfig": "58"}, {"size": 6593, "mtime": 1753877545875, "results": "71", "hashOfConfig": "58"}, {"size": 8884, "mtime": 1754028034221, "results": "72", "hashOfConfig": "58"}, {"size": 13020, "mtime": 1753932196267, "results": "73", "hashOfConfig": "58"}, {"size": 8832, "mtime": 1754028052223, "results": "74", "hashOfConfig": "58"}, {"size": 13473, "mtime": 1753258602482, "results": "75", "hashOfConfig": "58"}, {"size": 28048, "mtime": 1754376097174, "results": "76", "hashOfConfig": "58"}, {"filePath": "77", "messages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "79"}, "1ank110", {"filePath": "80", "messages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "79"}, {"filePath": "82", "messages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "79"}, {"filePath": "84", "messages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "79"}, {"filePath": "86", "messages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "79"}, {"filePath": "88", "messages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "79"}, {"filePath": "90", "messages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "79"}, {"filePath": "94", "messages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "79"}, {"filePath": "98", "messages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "79"}, {"filePath": "100", "messages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "79"}, {"filePath": "102", "messages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "79"}, {"filePath": "104", "messages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "116"}, "1yyu0qu", {"filePath": "117", "messages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "116"}, {"filePath": "119", "messages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "116"}, {"filePath": "121", "messages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "116"}, {"filePath": "123", "messages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "116"}, {"filePath": "125", "messages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "116"}, {"filePath": "129", "messages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "116"}, {"filePath": "131", "messages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "116"}, {"filePath": "137", "messages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "116"}, {"filePath": "139", "messages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "116"}, {"filePath": "141", "messages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "116"}, {"filePath": "143", "messages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "116"}, {"filePath": "145", "messages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "116"}, {"filePath": "147", "messages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "116"}, {"filePath": "149", "messages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/frontend-react-stable/src/index.tsx", [], ["153", "154"], "/home/<USER>/frontend-react-stable/src/store/store.ts", [], "/home/<USER>/frontend-react-stable/src/App.tsx", [], "/home/<USER>/frontend-react-stable/src/store/slices/authSlice.ts", [], "/home/<USER>/frontend-react-stable/src/store/slices/uiSlice.ts", [], "/home/<USER>/frontend-react-stable/src/pages/LoginPage.tsx", [], "/home/<USER>/frontend-react-stable/src/pages/CleanTemplatePage.tsx", [], "/home/<USER>/frontend-react-stable/src/pages/ModelPredictionPage.tsx", [], "/home/<USER>/frontend-react-stable/src/pages/ModelRegistryPage.tsx", [], "/home/<USER>/frontend-react-stable/src/pages/UserManagementPage.tsx", [], "/home/<USER>/frontend-react-stable/src/pages/DataCleaningPage.tsx", [], "/home/<USER>/frontend-react-stable/src/pages/ModelTrainingPage.tsx", [], "/home/<USER>/frontend-react-stable/src/pages/DataQueryPage.tsx", [], "/home/<USER>/frontend-react-stable/src/components/Layout/MainLayout.tsx", [], "/home/<USER>/frontend-react-stable/src/services/api.ts", [], "/home/<USER>/frontend-react-stable/src/hooks/useTaskManager.ts", ["155", "156", "157", "158", "159"], "/home/<USER>/frontend-react-stable/src/services/taskApi.ts", ["160"], "/home/<USER>/frontend-react-stable/src/components/TaskStatusIndicator.tsx", ["161"], "/home/<USER>/frontend-react-stable/src/index.tsx", [], ["162", "163"], "/home/<USER>/frontend-react-stable/src/store/store.ts", [], "/home/<USER>/frontend-react-stable/src/App.tsx", [], "/home/<USER>/frontend-react-stable/src/store/slices/uiSlice.ts", [], "/home/<USER>/frontend-react-stable/src/store/slices/authSlice.ts", [], "/home/<USER>/frontend-react-stable/src/pages/LoginPage.tsx", [], "/home/<USER>/frontend-react-stable/src/pages/DataCleaningPage.tsx", [], "/home/<USER>/frontend-react-stable/src/pages/ModelRegistryPage.tsx", [], "/home/<USER>/frontend-react-stable/src/pages/ModelTrainingPage.tsx", [], "/home/<USER>/frontend-react-stable/src/pages/ModelPredictionPage.tsx", [], "/home/<USER>/frontend-react-stable/src/pages/UserManagementPage.tsx", [], "/home/<USER>/frontend-react-stable/src/pages/DataQueryPage.tsx", [], "/home/<USER>/frontend-react-stable/src/pages/CleanTemplatePage.tsx", [], "/home/<USER>/frontend-react-stable/src/components/Layout/MainLayout.tsx", [], "/home/<USER>/frontend-react-stable/src/services/api.ts", [], "/home/<USER>/frontend-react-stable/src/hooks/useTaskManager.ts", [], "/home/<USER>/frontend-react-stable/src/services/taskApi.ts", [], "/home/<USER>/frontend-react-stable/src/components/TaskStatusIndicator.tsx", ["164", "165"], "/home/<USER>/frontend-react-stable/src/pages/TaskManagerPage.tsx", [], {"ruleId": "166", "replacedBy": "167"}, {"ruleId": "168", "replacedBy": "169"}, {"ruleId": "170", "severity": 1, "message": "171", "line": 12, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 12, "endColumn": 13}, {"ruleId": "170", "severity": 1, "message": "174", "line": 13, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 13, "endColumn": 11}, {"ruleId": "175", "severity": 1, "message": "176", "line": 164, "column": 7, "nodeType": "172", "messageId": "177", "endLine": 164, "endColumn": 28}, {"ruleId": "175", "severity": 1, "message": "176", "line": 194, "column": 7, "nodeType": "172", "messageId": "177", "endLine": 194, "endColumn": 28}, {"ruleId": "178", "severity": 1, "message": "179", "line": 262, "column": 24, "nodeType": "172", "endLine": 262, "endColumn": 31}, {"ruleId": "180", "severity": 1, "message": "181", "line": 254, "column": 1, "nodeType": "182", "endLine": 268, "endColumn": 3}, {"ruleId": "170", "severity": 1, "message": "183", "line": 25, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 25, "endColumn": 14}, {"ruleId": "166", "replacedBy": "184"}, {"ruleId": "168", "replacedBy": "185"}, {"ruleId": "170", "severity": 1, "message": "186", "line": 23, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 23, "endColumn": 10}, {"ruleId": "170", "severity": 1, "message": "187", "line": 49, "column": 5, "nodeType": "172", "messageId": "173", "endLine": 49, "endColumn": 24}, "no-native-reassign", ["188"], "no-negated-in-lhs", ["189"], "@typescript-eslint/no-unused-vars", "'TaskStatus' is defined but never used.", "Identifier", "unusedVar", "'TaskType' is defined but never used.", "@typescript-eslint/no-use-before-define", "'initializeTaskManager' was used before it was defined.", "noUseBeforeDefine", "react-hooks/exhaustive-deps", "The ref value 'pollingIntervals.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'pollingIntervals.current' to a variable inside the effect, and use that variable in the cleanup function.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'Task' is defined but never used.", ["188"], ["189"], "'Divider' is defined but never used.", "'fetchCompletedTasks' is assigned a value but never used.", "no-global-assign", "no-unsafe-negation"]